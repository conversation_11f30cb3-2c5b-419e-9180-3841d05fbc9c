import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { createServer } from 'http';
import { WebSocketServer as WSServer } from 'ws';

const app = express();
const server = createServer(app);
const PORT = process.env.API_PORT || 3005;

// Middleware
app.use(helmet());
app.use(cors());
app.use(compression());
app.use(express.json());

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // limit each IP to 1000 requests per windowMs
  message: 'Too many requests from this IP',
});
app.use(limiter);

// WebSocket Server Setup
const wss = new WSServer({ server, path: '/ws' });

// Mock data generators
const generateMockMetrics = () => ({
  totalVolume24h: 125000000 + Math.random() * 10000000,
  totalTrades24h: 45000 + Math.floor(Math.random() * 5000),
  activeWallets24h: 12500 + Math.floor(Math.random() * 1000),
  avgPnL24h: 2500 + Math.random() * 500,
  suspiciousActivities: Math.floor(Math.random() * 50),
  rugpullsDetected: Math.floor(Math.random() * 5),
});

const generateMockTrade = () => ({
  signature: Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15),
  walletAddress: '7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU',
  dex: ['raydium', 'jupiter', 'orca'][Math.floor(Math.random() * 3)],
  tradeType: ['buy', 'sell', 'swap'][Math.floor(Math.random() * 3)],
  tokenInSymbol: 'SOL',
  tokenOutSymbol: 'USDC',
  amountIn: Math.random() * 10,
  amountOut: Math.random() * 1000,
  price: 100 + Math.random() * 50,
  blockTime: new Date().toISOString(),
  isSuspicious: Math.random() < 0.1,
});

const generateMockAlert = () => ({
  id: Math.random().toString(36).substring(2, 10),
  type: ['whale_movement', 'volume_spike', 'rugpull_detected', 'suspicious_activity'][Math.floor(Math.random() * 4)],
  severity: ['low', 'medium', 'high', 'critical'][Math.floor(Math.random() * 4)],
  title: 'Trading Alert',
  description: 'Suspicious trading activity detected',
  timestamp: new Date().toISOString(),
  isResolved: false,
});

// WebSocket connection handling
wss.on('connection', (ws) => {
  console.log('WebSocket client connected');

  // Send initial data
  ws.send(JSON.stringify({
    type: 'metrics_update',
    data: generateMockMetrics()
  }));

  // Send periodic updates
  const metricsInterval = setInterval(() => {
    if (ws.readyState === ws.OPEN) {
      ws.send(JSON.stringify({
        type: 'metrics_update',
        data: generateMockMetrics()
      }));
    }
  }, 5000);

  const tradesInterval = setInterval(() => {
    if (ws.readyState === ws.OPEN) {
      ws.send(JSON.stringify({
        type: 'new_trade',
        data: generateMockTrade()
      }));
    }
  }, 3000);

  const alertsInterval = setInterval(() => {
    if (ws.readyState === ws.OPEN && Math.random() < 0.3) {
      ws.send(JSON.stringify({
        type: 'new_alert',
        data: generateMockAlert()
      }));
    }
  }, 10000);

  ws.on('close', () => {
    console.log('WebSocket client disconnected');
    clearInterval(metricsInterval);
    clearInterval(tradesInterval);
    clearInterval(alertsInterval);
  });

  ws.on('error', (error) => {
    console.error('WebSocket error:', error);
  });
});

// Routes
app.get('/', (req, res) => {
  res.json({
    name: 'Solana Trading Intelligence API',
    version: '1.0.0',
    description: 'Real-time trading intelligence and analytics for Solana blockchain',
    status: 'running',
    timestamp: new Date().toISOString(),
    endpoints: {
      health: '/health',
      auth: '/api/v1/auth',
    }
  });
});

app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: '1.0.0',
    services: {
      api: { status: 'healthy' },
    }
  });
});

// Auth routes
app.post('/api/v1/auth/login', (req, res) => {
  const { email, password } = req.body;

  if (!email || !password) {
    return res.status(400).json({
      error: 'Email and password are required'
    });
  }

  // Mock authentication
  if (email === '<EMAIL>' && password === 'admin123') {
    return res.json({
      success: true,
      token: 'mock-jwt-token',
      user: {
        id: '1',
        email: '<EMAIL>',
        name: 'Admin User',
        role: 'admin'
      }
    });
  } else {
    return res.status(401).json({
      error: 'Invalid credentials'
    });
  }
});

// Mock wallet data endpoint
app.get('/api/v1/wallets', (req, res) => {
  const mockWallets = [
    {
      address: '7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU',
      totalPnl: 125000.50,
      winRate: 0.78,
      totalTrades: 1250,
      totalVolume: 2500000.00,
      lastActive: new Date().toISOString()
    },
    {
      address: '9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM',
      totalPnl: 89000.25,
      winRate: 0.65,
      totalTrades: 890,
      totalVolume: 1800000.00,
      lastActive: new Date().toISOString()
    }
  ];

  res.json({
    success: true,
    data: {
      wallets: mockWallets,
      pagination: {
        page: 1,
        limit: 20,
        total: 2,
        pages: 1
      }
    }
  });
});

// Mock trades endpoint
app.get('/api/v1/trades', (req, res) => {
  const mockTrades = [
    {
      signature: '5VfYmGC9L2VTAhBjEhd4KGX9h8b4c3d2e1f0g9h8i7j6k5l4m3n2o1p0q9r8s7t6u5v4w3x2y1z0',
      walletAddress: '7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU',
      dex: 'raydium',
      tradeType: 'buy',
      tokenInMint: 'So11111111111111111111111111111111111111112',
      tokenOutMint: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
      amountIn: 1.5,
      amountOut: 1500.0,
      price: 1000.0,
      blockTime: new Date().toISOString(),
      isSuspicious: false
    }
  ];

  res.json({
    success: true,
    data: {
      trades: mockTrades,
      pagination: {
        page: 1,
        limit: 20,
        total: 1,
        pages: 1
      }
    }
  });
});

// API endpoints that the frontend expects
app.get('/api/metrics', (req, res) => {
  res.json({
    success: true,
    data: generateMockMetrics(),
    timestamp: new Date().toISOString(),
  });
});

app.get('/api/metrics/historical', (req, res) => {
  const { timeframe = '24h', interval = '1h' } = req.query;
  const dataPoints = timeframe === '24h' ? 24 : timeframe === '7d' ? 168 : 720;
  const data = [];

  for (let i = 0; i < dataPoints; i++) {
    data.push({
      timestamp: new Date(Date.now() - i * 3600000).toISOString(),
      volume: 5000000 + Math.random() * 2000000,
      trades: 1800 + Math.floor(Math.random() * 400),
      price: 100 + Math.random() * 20,
      activeWallets: 500 + Math.floor(Math.random() * 100),
    });
  }

  res.json({
    success: true,
    data: data.reverse(),
    timeframe,
    interval,
    timestamp: new Date().toISOString(),
  });
});

app.get('/api/trades', (req, res) => {
  const trades = Array.from({ length: 20 }, () => generateMockTrade());
  res.json({
    success: true,
    data: trades,
    timestamp: new Date().toISOString(),
  });
});

app.get('/api/alerts', (req, res) => {
  const alerts = Array.from({ length: 10 }, () => generateMockAlert());
  res.json({
    success: true,
    data: alerts,
    timestamp: new Date().toISOString(),
  });
});

app.get('/api/wallets/leaderboard', (req, res) => {
  const wallets = [
    {
      address: '7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU',
      totalPnl: 125000.50,
      winRate: 0.78,
      totalTrades: 1250,
      totalVolume: 2500000.00,
    },
    {
      address: '9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM',
      totalPnl: 89000.25,
      winRate: 0.65,
      totalTrades: 890,
      totalVolume: 1800000.00,
    },
    {
      address: 'BQcdHdAQW1hczDbBi9hiegXAR7A98Q9jx3X3iBBBDiq4',
      totalPnl: 67500.75,
      winRate: 0.72,
      totalTrades: 567,
      totalVolume: 1200000.00,
    },
  ];

  res.json({
    success: true,
    data: wallets,
    timestamp: new Date().toISOString(),
  });
});

// Error handling
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Error:', err);
  res.status(500).json({
    error: 'Internal server error',
    message: err.message
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Endpoint not found',
    message: `The requested endpoint ${req.originalUrl} does not exist`
  });
});

// Start server
server.listen(PORT, () => {
  console.log(`🚀 Solana Trading Intelligence API started on port ${PORT}`);
  console.log(`🔗 Health check: http://localhost:${PORT}/health`);
  console.log(`📊 API Status: http://localhost:${PORT}/`);
  console.log(`🔐 Login: POST http://localhost:${PORT}/api/v1/auth/login`);
  console.log(`💰 Wallets: GET http://localhost:${PORT}/api/v1/wallets`);
  console.log(`📈 Trades: GET http://localhost:${PORT}/api/v1/trades`);
  console.log(`🔌 WebSocket: ws://localhost:${PORT}/ws`);
});

export default app;
