import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { createServer } from 'http';
import { WebSocketServer } from 'ws';

const app = express();
const PORT = process.env.API_PORT || 3004;

// Middleware
app.use(helmet());
app.use(cors());
app.use(compression());
app.use(express.json());

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // limit each IP to 1000 requests per windowMs
  message: 'Too many requests from this IP',
});
app.use(limiter);

// Routes
app.get('/', (req, res) => {
  res.json({
    name: 'Solana Trading Intelligence API',
    version: '1.0.0',
    description: 'Real-time trading intelligence and analytics for Solana blockchain',
    status: 'running',
    timestamp: new Date().toISOString(),
    endpoints: {
      health: '/health',
      auth: '/api/v1/auth',
    }
  });
});

app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: '1.0.0',
    services: {
      api: { status: 'healthy' },
    }
  });
});

// Auth routes
app.post('/api/v1/auth/login', (req, res) => {
  const { email, password } = req.body;

  if (!email || !password) {
    return res.status(400).json({
      error: 'Email and password are required'
    });
  }

  // Mock authentication
  if (email === '<EMAIL>' && password === 'admin123') {
    return res.json({
      success: true,
      token: 'mock-jwt-token',
      user: {
        id: '1',
        email: '<EMAIL>',
        name: 'Admin User',
        role: 'admin'
      }
    });
  } else {
    return res.status(401).json({
      error: 'Invalid credentials'
    });
  }
});

// Mock wallet data endpoint
app.get('/api/v1/wallets', (req, res) => {
  const mockWallets = [
    {
      address: '7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU',
      totalPnl: 125000.50,
      winRate: 0.78,
      totalTrades: 1250,
      totalVolume: 2500000.00,
      lastActive: new Date().toISOString()
    },
    {
      address: '9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM',
      totalPnl: 89000.25,
      winRate: 0.65,
      totalTrades: 890,
      totalVolume: 1800000.00,
      lastActive: new Date().toISOString()
    }
  ];

  res.json({
    success: true,
    data: {
      wallets: mockWallets,
      pagination: {
        page: 1,
        limit: 20,
        total: 2,
        pages: 1
      }
    }
  });
});

// Mock trades endpoint
app.get('/api/v1/trades', (req, res) => {
  const mockTrades = [
    {
      signature: '5VfYmGC9L2VTAhBjEhd4KGX9h8b4c3d2e1f0g9h8i7j6k5l4m3n2o1p0q9r8s7t6u5v4w3x2y1z0',
      walletAddress: '7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU',
      dex: 'raydium',
      tradeType: 'buy',
      tokenInMint: 'So11111111111111111111111111111111111111112',
      tokenOutMint: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
      amountIn: 1.5,
      amountOut: 1500.0,
      price: 1000.0,
      blockTime: new Date().toISOString(),
      isSuspicious: false
    }
  ];

  res.json({
    success: true,
    data: {
      trades: mockTrades,
      pagination: {
        page: 1,
        limit: 20,
        total: 1,
        pages: 1
      }
    }
  });
});

// Error handling
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Error:', err);
  res.status(500).json({
    error: 'Internal server error',
    message: err.message
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Endpoint not found',
    message: `The requested endpoint ${req.originalUrl} does not exist`
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Solana Trading Intelligence API started on port ${PORT}`);
  console.log(`🔗 Health check: http://localhost:${PORT}/health`);
  console.log(`📊 API Status: http://localhost:${PORT}/`);
  console.log(`🔐 Login: POST http://localhost:${PORT}/api/v1/auth/login`);
  console.log(`💰 Wallets: GET http://localhost:${PORT}/api/v1/wallets`);
  console.log(`📈 Trades: GET http://localhost:${PORT}/api/v1/trades`);
});

export default app;
