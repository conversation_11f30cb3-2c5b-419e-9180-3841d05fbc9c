{"name": "solana-trading-dashboard", "version": "1.0.0", "description": "Real-time trading intelligence dashboard for Solana blockchain", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "analyze": "cross-env ANALYZE=true next build", "export": "next export", "deploy:netlify": "npm run build && netlify deploy --prod --dir=out"}, "dependencies": {"@emotion/cache": "^11.14.0", "@emotion/react": "^11.11.1", "@emotion/server": "^11.11.0", "@emotion/styled": "^11.11.0", "@hookform/resolvers": "^3.2.0", "@mui/icons-material": "^5.14.3", "@mui/material": "^5.14.5", "@mui/x-charts": "^6.0.0-alpha.18", "@mui/x-data-grid": "^6.10.2", "@mui/x-date-pickers": "^6.10.2", "@next/font": "^13.4.19", "@tanstack/react-query": "^4.32.6", "axios": "^1.5.0", "big.js": "^6.2.1", "chart.js": "^4.3.3", "critters": "^0.0.23", "decimal.js": "^10.4.3", "framer-motion": "^10.16.1", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "moment": "^2.29.4", "next": "^13.4.19", "next-pwa": "^5.6.0", "next-seo": "^6.1.0", "next-sitemap": "^4.2.3", "numeral": "^2.0.6", "qrcode": "^1.5.3", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^18.2.0", "react-helmet-async": "^1.3.0", "react-hook-form": "^7.45.4", "react-hot-toast": "^2.4.1", "react-intersection-observer": "^9.5.2", "react-qr-code": "^2.0.11", "react-query": "^3.39.3", "react-virtualized-auto-sizer": "^1.0.20", "react-window": "^1.8.8", "recharts": "^2.8.0", "socket.io-client": "^4.7.2", "yup": "^1.2.0"}, "devDependencies": {"@next/bundle-analyzer": "^13.4.19", "@tailwindcss/forms": "^0.5.4", "@tailwindcss/typography": "^0.5.9", "@testing-library/jest-dom": "^6.1.2", "@testing-library/react": "^13.4.0", "@types/js-cookie": "^3.0.3", "@types/lodash": "^4.14.195", "@types/node": "^20.5.0", "@types/numeral": "^2.0.2", "@types/qrcode": "^1.5.1", "@types/react": "^18.2.21", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.4.0", "@typescript-eslint/parser": "^6.4.0", "autoprefixer": "^10.4.15", "cross-env": "^7.0.3", "eslint": "^8.47.0", "eslint-config-next": "^13.4.19", "jest": "^29.6.2", "jest-environment-jsdom": "^29.6.2", "postcss": "^8.4.28", "prettier": "^3.0.2", "tailwindcss": "^3.3.3", "typescript": "^5.1.6"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}