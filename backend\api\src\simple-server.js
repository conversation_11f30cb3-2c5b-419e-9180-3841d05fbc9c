const express = require('express');
const cors = require('cors');
const helmet = require('helmet');

const app = express();
const PORT = process.env.PORT || 3005;

// Middleware
app.use(helmet());
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:3002'],
  credentials: true,
}));
app.use(express.json());

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: '1.0.0',
  });
});

// Mock data generators
const generateMockMetrics = () => ({
  totalVolume24h: 125000000 + Math.random() * 10000000,
  totalTrades24h: 45000 + Math.floor(Math.random() * 5000),
  activeWallets24h: 12500 + Math.floor(Math.random() * 1000),
  avgPnL24h: 2500 + Math.random() * 500,
  suspiciousActivities: Math.floor(Math.random() * 50),
  rugpullsDetected: Math.floor(Math.random() * 5),
  topDex: 'Raydium',
  priceChange24h: (Math.random() - 0.5) * 20,
  marketCap: 4********** + Math.random() * **********,
});

const generateMockTrade = () => {
  const dexes = ['Raydium', 'Orca', 'Jupiter', 'Serum'];
  const tokens = ['SOL', 'USDC', 'RAY', 'ORCA', 'SRM', 'MNGO'];
  const tradeTypes = ['buy', 'sell', 'swap'];
  
  return {
    signature: Math.random().toString(36).substring(2, 90),
    walletAddress: Math.random().toString(36).substring(2, 46),
    dex: dexes[Math.floor(Math.random() * dexes.length)],
    tradeType: tradeTypes[Math.floor(Math.random() * tradeTypes.length)],
    tokenInSymbol: tokens[Math.floor(Math.random() * tokens.length)],
    tokenOutSymbol: tokens[Math.floor(Math.random() * tokens.length)],
    amountIn: Math.random() * 10000,
    amountOut: Math.random() * 10000,
    price: Math.random() * 100,
    blockTime: new Date(Date.now() - Math.random() * 86400000).toISOString(),
    isSuspicious: Math.random() < 0.05,
    fee: Math.random() * 10,
    slippage: Math.random() * 5,
  };
};

const generateMockAlert = () => {
  const types = ['whale_movement', 'rugpull_detected', 'unusual_volume', 'price_spike'];
  const severities = ['low', 'medium', 'high', 'critical'];
  
  return {
    id: Math.random().toString(36).substring(2, 10),
    type: types[Math.floor(Math.random() * types.length)],
    severity: severities[Math.floor(Math.random() * severities.length)],
    title: 'Trading Alert',
    description: 'Suspicious trading activity detected',
    timestamp: new Date().toISOString(),
    isResolved: false,
    metadata: {
      amount: Math.random() * 100000,
      token: 'SOL',
    }
  };
};

// API Routes
app.get('/api/metrics', (req, res) => {
  res.json({
    success: true,
    data: generateMockMetrics(),
    timestamp: new Date().toISOString(),
  });
});

app.get('/api/metrics/historical', (req, res) => {
  const { timeframe = '24h', interval = '1h' } = req.query;
  const dataPoints = timeframe === '24h' ? 24 : timeframe === '7d' ? 168 : 720;
  const data = [];
  
  for (let i = 0; i < dataPoints; i++) {
    data.push({
      timestamp: new Date(Date.now() - i * 3600000).toISOString(),
      volume: 5000000 + Math.random() * 2000000,
      trades: 1800 + Math.floor(Math.random() * 400),
      price: 100 + Math.random() * 20,
      activeWallets: 500 + Math.floor(Math.random() * 100),
    });
  }
  
  res.json({
    success: true,
    data: data.reverse(),
    timeframe,
    interval,
    timestamp: new Date().toISOString(),
  });
});

app.get('/api/trades', (req, res) => {
  const { limit = 50 } = req.query;
  const trades = [];
  
  for (let i = 0; i < parseInt(limit); i++) {
    trades.push(generateMockTrade());
  }
  
  res.json({
    success: true,
    data: trades,
    timestamp: new Date().toISOString(),
  });
});

app.get('/api/alerts', (req, res) => {
  const { limit = 20 } = req.query;
  const alerts = [];
  
  for (let i = 0; i < parseInt(limit); i++) {
    alerts.push(generateMockAlert());
  }
  
  res.json({
    success: true,
    data: alerts,
    timestamp: new Date().toISOString(),
  });
});

app.get('/api/wallets/leaderboard', (req, res) => {
  const { limit = 50 } = req.query;
  const wallets = [];
  
  for (let i = 0; i < parseInt(limit); i++) {
    wallets.push({
      rank: i + 1,
      address: Math.random().toString(36).substring(2, 46),
      pnl: Math.random() * 100000 - 50000,
      volume: Math.random() * 10000000,
      trades: Math.floor(Math.random() * 1000) + 10,
      winRate: 0.3 + Math.random() * 0.5,
      avgTradeSize: Math.random() * 10000 + 100,
      lastActive: new Date(Date.now() - Math.random() * 86400000).toISOString(),
    });
  }
  
  res.json({
    success: true,
    data: wallets,
    timestamp: new Date().toISOString(),
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Not Found',
    message: `Route ${req.originalUrl} not found`,
    timestamp: new Date().toISOString(),
  });
});

// Error handler
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    success: false,
    error: 'Internal Server Error',
    message: err.message,
    timestamp: new Date().toISOString(),
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Solana Trading API Server running on port ${PORT}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🔗 Health check: http://localhost:${PORT}/health`);
});

module.exports = app;
