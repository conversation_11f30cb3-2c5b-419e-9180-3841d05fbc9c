import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import toast from 'react-hot-toast';

// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3005';
const API_TIMEOUT = 30000; // 30 seconds
const RETRY_ATTEMPTS = 3;
const RETRY_DELAY = 1000; // 1 second

// API Response Types
interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  timestamp: string;
  pagination?: {
    limit: number;
    offset: number;
    total: number;
  };
}

interface ApiError {
  success: false;
  error: string;
  message: string;
  statusCode: number;
  timestamp: string;
}

// Rate Limiting
class RateLimiter {
  private requests: Map<string, number[]> = new Map();
  private readonly maxRequests = 100;
  private readonly windowMs = 15 * 60 * 1000; // 15 minutes

  canMakeRequest(endpoint: string): boolean {
    const now = Date.now();
    const requests = this.requests.get(endpoint) || [];

    // Remove old requests outside the window
    const validRequests = requests.filter(time => now - time < this.windowMs);

    if (validRequests.length >= this.maxRequests) {
      return false;
    }

    validRequests.push(now);
    this.requests.set(endpoint, validRequests);
    return true;
  }

  getRemainingRequests(endpoint: string): number {
    const now = Date.now();
    const requests = this.requests.get(endpoint) || [];
    const validRequests = requests.filter(time => now - time < this.windowMs);
    return Math.max(0, this.maxRequests - validRequests.length);
  }
}

const rateLimiter = new RateLimiter();

// Retry Logic
const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

async function retryRequest<T>(
  requestFn: () => Promise<T>,
  attempts: number = RETRY_ATTEMPTS,
  delay: number = RETRY_DELAY
): Promise<T> {
  try {
    return await requestFn();
  } catch (error: any) {
    if (attempts <= 1) {
      throw error;
    }

    // Only retry on network errors or 5xx status codes
    const shouldRetry = !error.response ||
      (error.response.status >= 500 && error.response.status < 600) ||
      error.code === 'NETWORK_ERROR' ||
      error.code === 'TIMEOUT';

    if (!shouldRetry) {
      throw error;
    }

    await sleep(delay);
    return retryRequest(requestFn, attempts - 1, delay * 2);
  }
}

// Create Axios Instance
const createApiInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: API_BASE_URL,
    timeout: API_TIMEOUT,
    headers: {
      'Content-Type': 'application/json',
      'X-API-Key': process.env.NEXT_PUBLIC_API_KEY || 'dev-key-123',
    },
  });

  // Request Interceptor
  instance.interceptors.request.use(
    (config) => {
      // Rate limiting check
      const endpoint = config.url || '';
      if (!rateLimiter.canMakeRequest(endpoint)) {
        const remaining = rateLimiter.getRemainingRequests(endpoint);
        throw new Error(`Rate limit exceeded. ${remaining} requests remaining.`);
      }

      // Add timestamp for request tracking
      config.metadata = { startTime: Date.now() };

      // Add auth token if available
      if (typeof window !== 'undefined') {
        const token = localStorage.getItem('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
      }

      // Add request ID for tracking
      config.headers['X-Request-ID'] = Math.random().toString(36).substring(2, 11);

      return config;
    },
    (error) => {
      console.error('Request interceptor error:', error);
      return Promise.reject(error);
    }
  );

  // Response Interceptor
  instance.interceptors.response.use(
    (response: AxiosResponse) => {
      // Log response time in development
      if (process.env.NODE_ENV === 'development') {
        const duration = Date.now() - (response.config.metadata?.startTime || 0);
        console.log(`API ${response.config.method?.toUpperCase()} ${response.config.url}: ${duration}ms`);
      }

      return response.data;
    },
    (error) => {
      // Enhanced error handling
      const errorResponse: ApiError = {
        success: false,
        error: 'Unknown Error',
        message: 'An unexpected error occurred',
        statusCode: 500,
        timestamp: new Date().toISOString(),
      };

      if (error.response) {
        // Server responded with error status
        errorResponse.statusCode = error.response.status;
        errorResponse.error = error.response.data?.error || 'Server Error';
        errorResponse.message = error.response.data?.message || error.message;

        // Handle specific status codes
        switch (error.response.status) {
          case 401:
            errorResponse.message = 'Authentication required';
            if (typeof window !== 'undefined') {
              localStorage.removeItem('auth_token');
              toast.error('Session expired. Please log in again.');
            }
            break;
          case 403:
            errorResponse.message = 'Access forbidden';
            toast.error('You do not have permission to perform this action.');
            break;
          case 404:
            errorResponse.message = 'Resource not found';
            break;
          case 429:
            errorResponse.message = 'Too many requests. Please try again later.';
            toast.error('Rate limit exceeded. Please slow down.');
            break;
          case 500:
            errorResponse.message = 'Internal server error';
            toast.error('Server error. Please try again later.');
            break;
          default:
            toast.error(errorResponse.message);
        }
      } else if (error.request) {
        // Network error
        errorResponse.error = 'Network Error';
        errorResponse.message = 'Unable to connect to server. Please check your internet connection.';
        toast.error('Network error. Please check your connection.');
      } else {
        // Request setup error
        errorResponse.error = 'Request Error';
        errorResponse.message = error.message;
      }

      console.error('API Error:', errorResponse);
      return Promise.reject(errorResponse);
    }
  );

  return instance;
};

const api = createApiInstance();

// API Service Methods
export const apiService = {
  // Metrics endpoints
  metrics: {
    getRealTime: () => retryRequest(() => api.get('/api/metrics')),
    getHistorical: (timeframe: string, interval: string) =>
      retryRequest(() => api.get(`/api/metrics/historical?timeframe=${timeframe}&interval=${interval}`)),
    getVolume: (dex?: string, token?: string) =>
      retryRequest(() => api.get(`/api/metrics/volume?dex=${dex || ''}&token=${token || ''}`)),
    getPnL: (wallet: string, timeframe: string) =>
      retryRequest(() => api.get(`/api/metrics/pnl?wallet=${wallet}&timeframe=${timeframe}`)),
    getDexComparison: (timeframe: string) =>
      retryRequest(() => api.get(`/api/metrics/dex-comparison?timeframe=${timeframe}`)),
    getTopTokens: (limit: number, timeframe: string) =>
      retryRequest(() => api.get(`/api/metrics/top-tokens?limit=${limit}&timeframe=${timeframe}`)),
  },

  // Trades endpoints
  trades: {
    getRecent: (params: any) => retryRequest(() => api.get('/api/trades', { params })),
    getBySignature: (signature: string) => retryRequest(() => api.get(`/api/trades/${signature}`)),
    getByWallet: (address: string, params: any) =>
      retryRequest(() => api.get(`/api/trades/wallet/${address}`, { params })),
    getWhales: (params: any) => retryRequest(() => api.get('/api/trades/whales', { params })),
    getSuspicious: (params: any) => retryRequest(() => api.get('/api/trades/suspicious', { params })),
    getAnalytics: (timeframe: string) =>
      retryRequest(() => api.get(`/api/trades/analytics/summary?timeframe=${timeframe}`)),
  },

  // Alerts endpoints
  alerts: {
    getActive: (params: any) => retryRequest(() => api.get('/api/alerts', { params })),
    getById: (id: string) => retryRequest(() => api.get(`/api/alerts/${id}`)),
    resolve: (id: string) => retryRequest(() => api.patch(`/api/alerts/${id}/resolve`)),
  },

  // Wallets endpoints
  wallets: {
    getLeaderboard: (params: any) => retryRequest(() => api.get('/api/wallets/leaderboard', { params })),
    getDetails: (address: string, timeframe: string) =>
      retryRequest(() => api.get(`/api/wallets/${address}?timeframe=${timeframe}`)),
    getTrades: (address: string, params: any) =>
      retryRequest(() => api.get(`/api/wallets/${address}/trades`, { params })),
    getPnL: (address: string, timeframe: string, interval: string) =>
      retryRequest(() => api.get(`/api/wallets/${address}/pnl?timeframe=${timeframe}&interval=${interval}`)),
  },

  // Analytics endpoints
  analytics: {
    getMarket: (timeframe: string) =>
      retryRequest(() => api.get(`/api/analytics/market?timeframe=${timeframe}`)),
    getToken: (symbol: string, timeframe: string) =>
      retryRequest(() => api.get(`/api/analytics/tokens/${symbol}?timeframe=${timeframe}`)),
    getDex: (name: string, timeframe: string) =>
      retryRequest(() => api.get(`/api/analytics/dex/${name}?timeframe=${timeframe}`)),
    getWhales: (timeframe: string, minAmount: number) =>
      retryRequest(() => api.get(`/api/analytics/whales?timeframe=${timeframe}&minAmount=${minAmount}`)),
  },

  // Health check
  health: () => retryRequest(() => api.get('/health')),
};

// Export types
export type { ApiResponse, ApiError };

// Export default api instance
export default api;


