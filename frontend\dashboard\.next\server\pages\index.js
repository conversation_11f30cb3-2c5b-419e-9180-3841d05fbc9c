"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "__barrel_optimize__?names=Alert,AlertTitle,Box,Button,Card,CardContent,Stack,Typography!=!../../node_modules/@mui/material/index.js":
/*!*******************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Alert,AlertTitle,Box,Button,Card,CardContent,Stack,Typography!=!../../node_modules/@mui/material/index.js ***!
  \*******************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Alert: () => (/* reexport default from dynamic */ _Alert__WEBPACK_IMPORTED_MODULE_0___default.a),\n/* harmony export */   AlertTitle: () => (/* reexport default from dynamic */ _AlertTitle__WEBPACK_IMPORTED_MODULE_1___default.a),\n/* harmony export */   Box: () => (/* reexport default from dynamic */ _Box__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   Button: () => (/* reexport default from dynamic */ _Button__WEBPACK_IMPORTED_MODULE_3___default.a),\n/* harmony export */   Card: () => (/* reexport default from dynamic */ _Card__WEBPACK_IMPORTED_MODULE_4___default.a),\n/* harmony export */   CardContent: () => (/* reexport default from dynamic */ _CardContent__WEBPACK_IMPORTED_MODULE_5___default.a),\n/* harmony export */   Stack: () => (/* reexport safe */ _Stack__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   Typography: () => (/* reexport default from dynamic */ _Typography__WEBPACK_IMPORTED_MODULE_7___default.a)\n/* harmony export */ });\n/* harmony import */ var _Alert__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Alert */ \"../../node_modules/@mui/material/node/Alert/index.js\");\n/* harmony import */ var _Alert__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Alert__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _AlertTitle__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./AlertTitle */ \"../../node_modules/@mui/material/node/AlertTitle/index.js\");\n/* harmony import */ var _AlertTitle__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_AlertTitle__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Box__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Box */ \"../../node_modules/@mui/material/node/Box/index.js\");\n/* harmony import */ var _Box__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_Box__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Button */ \"../../node_modules/@mui/material/node/Button/index.js\");\n/* harmony import */ var _Button__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_Button__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _Card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Card */ \"../../node_modules/@mui/material/node/Card/index.js\");\n/* harmony import */ var _Card__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_Card__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _CardContent__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./CardContent */ \"../../node_modules/@mui/material/node/CardContent/index.js\");\n/* harmony import */ var _CardContent__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_CardContent__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _Stack__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Stack */ \"../../node_modules/@mui/material/node/Stack/index.js\");\n/* harmony import */ var _Typography__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./Typography */ \"../../node_modules/@mui/material/node/Typography/index.js\");\n/* harmony import */ var _Typography__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_Typography__WEBPACK_IMPORTED_MODULE_7__);\n/**\n * @mui/material v5.18.0\n *\n * @license MIT\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */ /* eslint-disable import/export */ \n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BbGVydCxBbGVydFRpdGxlLEJveCxCdXR0b24sQ2FyZCxDYXJkQ29udGVudCxTdGFjayxUeXBvZ3JhcGh5IT0hLi4vLi4vbm9kZV9tb2R1bGVzL0BtdWkvbWF0ZXJpYWwvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUMwQztBQUNVO0FBQ2Q7QUFDTTtBQUNKO0FBQ2M7QUFDWiIsInNvdXJjZXMiOlsid2VicGFjazovL3NvbGFuYS10cmFkaW5nLWRhc2hib2FyZC8uLi8uLi9ub2RlX21vZHVsZXMvQG11aS9tYXRlcmlhbC9pbmRleC5qcz8xODk3Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQG11aS9tYXRlcmlhbCB2NS4xOC4wXG4gKlxuICogQGxpY2Vuc2UgTUlUXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZSBmb3VuZCBpbiB0aGVcbiAqIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqLyAvKiBlc2xpbnQtZGlzYWJsZSBpbXBvcnQvZXhwb3J0ICovIFxuZXhwb3J0IHsgZGVmYXVsdCBhcyBBbGVydCB9IGZyb20gXCIuL0FsZXJ0XCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQWxlcnRUaXRsZSB9IGZyb20gXCIuL0FsZXJ0VGl0bGVcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCb3ggfSBmcm9tIFwiLi9Cb3hcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCdXR0b24gfSBmcm9tIFwiLi9CdXR0b25cIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDYXJkIH0gZnJvbSBcIi4vQ2FyZFwiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENhcmRDb250ZW50IH0gZnJvbSBcIi4vQ2FyZENvbnRlbnRcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTdGFjayB9IGZyb20gXCIuL1N0YWNrXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgVHlwb2dyYXBoeSB9IGZyb20gXCIuL1R5cG9ncmFwaHlcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Alert,AlertTitle,Box,Button,Card,CardContent,Stack,Typography!=!../../node_modules/@mui/material/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=AppBar,Box,Chip,Container,IconButton,Toolbar,Typography!=!../../node_modules/@mui/material/index.js":
/*!*************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=AppBar,Box,Chip,Container,IconButton,Toolbar,Typography!=!../../node_modules/@mui/material/index.js ***!
  \*************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppBar: () => (/* reexport default from dynamic */ _AppBar__WEBPACK_IMPORTED_MODULE_0___default.a),\n/* harmony export */   Box: () => (/* reexport default from dynamic */ _Box__WEBPACK_IMPORTED_MODULE_1___default.a),\n/* harmony export */   Chip: () => (/* reexport default from dynamic */ _Chip__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   Container: () => (/* reexport default from dynamic */ _Container__WEBPACK_IMPORTED_MODULE_3___default.a),\n/* harmony export */   IconButton: () => (/* reexport default from dynamic */ _IconButton__WEBPACK_IMPORTED_MODULE_4___default.a),\n/* harmony export */   Toolbar: () => (/* reexport default from dynamic */ _Toolbar__WEBPACK_IMPORTED_MODULE_5___default.a),\n/* harmony export */   Typography: () => (/* reexport default from dynamic */ _Typography__WEBPACK_IMPORTED_MODULE_6___default.a)\n/* harmony export */ });\n/* harmony import */ var _AppBar__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./AppBar */ \"../../node_modules/@mui/material/node/AppBar/index.js\");\n/* harmony import */ var _AppBar__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_AppBar__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Box__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Box */ \"../../node_modules/@mui/material/node/Box/index.js\");\n/* harmony import */ var _Box__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_Box__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Chip__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Chip */ \"../../node_modules/@mui/material/node/Chip/index.js\");\n/* harmony import */ var _Chip__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_Chip__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Container__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Container */ \"../../node_modules/@mui/material/node/Container/index.js\");\n/* harmony import */ var _Container__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_Container__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _IconButton__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./IconButton */ \"../../node_modules/@mui/material/node/IconButton/index.js\");\n/* harmony import */ var _IconButton__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_IconButton__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _Toolbar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Toolbar */ \"../../node_modules/@mui/material/node/Toolbar/index.js\");\n/* harmony import */ var _Toolbar__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_Toolbar__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _Typography__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Typography */ \"../../node_modules/@mui/material/node/Typography/index.js\");\n/* harmony import */ var _Typography__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_Typography__WEBPACK_IMPORTED_MODULE_6__);\n/**\n * @mui/material v5.18.0\n *\n * @license MIT\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */ /* eslint-disable import/export */ \n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BcHBCYXIsQm94LENoaXAsQ29udGFpbmVyLEljb25CdXR0b24sVG9vbGJhcixUeXBvZ3JhcGh5IT0hLi4vLi4vbm9kZV9tb2R1bGVzL0BtdWkvbWF0ZXJpYWwvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDNEM7QUFDTjtBQUNFO0FBQ1U7QUFDRTtBQUNOIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc29sYW5hLXRyYWRpbmctZGFzaGJvYXJkLy4uLy4uL25vZGVfbW9kdWxlcy9AbXVpL21hdGVyaWFsL2luZGV4LmpzPzJhMDgiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbXVpL21hdGVyaWFsIHY1LjE4LjBcbiAqXG4gKiBAbGljZW5zZSBNSVRcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIE1JVCBsaWNlbnNlIGZvdW5kIGluIHRoZVxuICogTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICovIC8qIGVzbGludC1kaXNhYmxlIGltcG9ydC9leHBvcnQgKi8gXG5leHBvcnQgeyBkZWZhdWx0IGFzIEFwcEJhciB9IGZyb20gXCIuL0FwcEJhclwiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEJveCB9IGZyb20gXCIuL0JveFwiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENoaXAgfSBmcm9tIFwiLi9DaGlwXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ29udGFpbmVyIH0gZnJvbSBcIi4vQ29udGFpbmVyXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgSWNvbkJ1dHRvbiB9IGZyb20gXCIuL0ljb25CdXR0b25cIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBUb29sYmFyIH0gZnJvbSBcIi4vVG9vbGJhclwiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFR5cG9ncmFwaHkgfSBmcm9tIFwiLi9UeXBvZ3JhcGh5XCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=AppBar,Box,Chip,Container,IconButton,Toolbar,Typography!=!../../node_modules/@mui/material/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=Box,Card,CardContent,CardHeader,Chip,Container,Grid,IconButton,Skeleton,Typography,useMediaQuery,useTheme!=!../../node_modules/@mui/material/index.js":
/*!***************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Box,Card,CardContent,CardHeader,Chip,Container,Grid,IconButton,Skeleton,Typography,useMediaQuery,useTheme!=!../../node_modules/@mui/material/index.js ***!
  \***************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Box: () => (/* reexport default from dynamic */ _Box__WEBPACK_IMPORTED_MODULE_0___default.a),\n/* harmony export */   Card: () => (/* reexport default from dynamic */ _Card__WEBPACK_IMPORTED_MODULE_1___default.a),\n/* harmony export */   CardContent: () => (/* reexport default from dynamic */ _CardContent__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   CardHeader: () => (/* reexport default from dynamic */ _CardHeader__WEBPACK_IMPORTED_MODULE_3___default.a),\n/* harmony export */   Chip: () => (/* reexport default from dynamic */ _Chip__WEBPACK_IMPORTED_MODULE_4___default.a),\n/* harmony export */   Container: () => (/* reexport default from dynamic */ _Container__WEBPACK_IMPORTED_MODULE_5___default.a),\n/* harmony export */   Grid: () => (/* reexport default from dynamic */ _Grid__WEBPACK_IMPORTED_MODULE_6___default.a),\n/* harmony export */   IconButton: () => (/* reexport default from dynamic */ _IconButton__WEBPACK_IMPORTED_MODULE_7___default.a),\n/* harmony export */   Skeleton: () => (/* reexport default from dynamic */ _Skeleton__WEBPACK_IMPORTED_MODULE_8___default.a),\n/* harmony export */   Typography: () => (/* reexport default from dynamic */ _Typography__WEBPACK_IMPORTED_MODULE_9___default.a),\n/* harmony export */   useMediaQuery: () => (/* reexport safe */ _useMediaQuery__WEBPACK_IMPORTED_MODULE_10__[\"default\"]),\n/* harmony export */   useTheme: () => (/* reexport safe */ E_Project_Crypto_Solana_Real_Time_Trading_Intelligence_System_node_modules_mui_material_styles_index_js__WEBPACK_IMPORTED_MODULE_11__.useTheme)\n/* harmony export */ });\n/* harmony import */ var _Box__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Box */ \"../../node_modules/@mui/material/node/Box/index.js\");\n/* harmony import */ var _Box__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Box__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Card */ \"../../node_modules/@mui/material/node/Card/index.js\");\n/* harmony import */ var _Card__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_Card__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _CardContent__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CardContent */ \"../../node_modules/@mui/material/node/CardContent/index.js\");\n/* harmony import */ var _CardContent__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_CardContent__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _CardHeader__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CardHeader */ \"../../node_modules/@mui/material/node/CardHeader/index.js\");\n/* harmony import */ var _CardHeader__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_CardHeader__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _Chip__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Chip */ \"../../node_modules/@mui/material/node/Chip/index.js\");\n/* harmony import */ var _Chip__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_Chip__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _Container__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Container */ \"../../node_modules/@mui/material/node/Container/index.js\");\n/* harmony import */ var _Container__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_Container__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _Grid__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Grid */ \"../../node_modules/@mui/material/node/Grid/index.js\");\n/* harmony import */ var _Grid__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_Grid__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _IconButton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./IconButton */ \"../../node_modules/@mui/material/node/IconButton/index.js\");\n/* harmony import */ var _IconButton__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_IconButton__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _Skeleton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./Skeleton */ \"../../node_modules/@mui/material/node/Skeleton/index.js\");\n/* harmony import */ var _Skeleton__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_Skeleton__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _Typography__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./Typography */ \"../../node_modules/@mui/material/node/Typography/index.js\");\n/* harmony import */ var _Typography__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_Typography__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _useMediaQuery__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./useMediaQuery */ \"../../node_modules/@mui/material/node/useMediaQuery/index.js\");\n/* harmony import */ var E_Project_Crypto_Solana_Real_Time_Trading_Intelligence_System_node_modules_mui_material_styles_index_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../node_modules/@mui/material/styles/index.js */ \"../../node_modules/@mui/material/styles/index.js\");\n/**\n * @mui/material v5.18.0\n *\n * @license MIT\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */ /* eslint-disable import/export */ \n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1Cb3gsQ2FyZCxDYXJkQ29udGVudCxDYXJkSGVhZGVyLENoaXAsQ29udGFpbmVyLEdyaWQsSWNvbkJ1dHRvbixTa2VsZXRvbixUeXBvZ3JhcGh5LHVzZU1lZGlhUXVlcnksdXNlVGhlbWUhPSEuLi8uLi9ub2RlX21vZHVsZXMvQG11aS9tYXRlcmlhbC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDc0M7QUFDRTtBQUNjO0FBQ0Y7QUFDWjtBQUNVO0FBQ1Y7QUFDWTtBQUNKO0FBQ0k7QUFDTSIsInNvdXJjZXMiOlsid2VicGFjazovL3NvbGFuYS10cmFkaW5nLWRhc2hib2FyZC8uLi8uLi9ub2RlX21vZHVsZXMvQG11aS9tYXRlcmlhbC9pbmRleC5qcz8xNDM0Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQG11aS9tYXRlcmlhbCB2NS4xOC4wXG4gKlxuICogQGxpY2Vuc2UgTUlUXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZSBmb3VuZCBpbiB0aGVcbiAqIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqLyAvKiBlc2xpbnQtZGlzYWJsZSBpbXBvcnQvZXhwb3J0ICovIFxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCb3ggfSBmcm9tIFwiLi9Cb3hcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDYXJkIH0gZnJvbSBcIi4vQ2FyZFwiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENhcmRDb250ZW50IH0gZnJvbSBcIi4vQ2FyZENvbnRlbnRcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDYXJkSGVhZGVyIH0gZnJvbSBcIi4vQ2FyZEhlYWRlclwiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENoaXAgfSBmcm9tIFwiLi9DaGlwXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ29udGFpbmVyIH0gZnJvbSBcIi4vQ29udGFpbmVyXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgR3JpZCB9IGZyb20gXCIuL0dyaWRcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBJY29uQnV0dG9uIH0gZnJvbSBcIi4vSWNvbkJ1dHRvblwiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFNrZWxldG9uIH0gZnJvbSBcIi4vU2tlbGV0b25cIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBUeXBvZ3JhcGh5IH0gZnJvbSBcIi4vVHlwb2dyYXBoeVwiXG5leHBvcnQgeyBkZWZhdWx0IGFzIHVzZU1lZGlhUXVlcnkgfSBmcm9tIFwiLi91c2VNZWRpYVF1ZXJ5XCJcbmV4cG9ydCB7IHVzZVRoZW1lIH0gZnJvbSBcIkU6XFxcXFByb2plY3RcXFxcQ3J5cHRvXFxcXFNvbGFuYSBSZWFsLVRpbWUgVHJhZGluZyBJbnRlbGxpZ2VuY2UgU3lzdGVtXFxcXG5vZGVfbW9kdWxlc1xcXFxAbXVpXFxcXG1hdGVyaWFsXFxcXHN0eWxlc1xcXFxpbmRleC5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Box,Card,CardContent,CardHeader,Chip,Container,Grid,IconButton,Skeleton,Typography,useMediaQuery,useTheme!=!../../node_modules/@mui/material/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=Box,Card,CardContent,Chip,Typography!=!../../node_modules/@mui/material/index.js":
/*!******************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Box,Card,CardContent,Chip,Typography!=!../../node_modules/@mui/material/index.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Box: () => (/* reexport default from dynamic */ _Box__WEBPACK_IMPORTED_MODULE_0___default.a),\n/* harmony export */   Card: () => (/* reexport default from dynamic */ _Card__WEBPACK_IMPORTED_MODULE_1___default.a),\n/* harmony export */   CardContent: () => (/* reexport default from dynamic */ _CardContent__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   Chip: () => (/* reexport default from dynamic */ _Chip__WEBPACK_IMPORTED_MODULE_3___default.a),\n/* harmony export */   Typography: () => (/* reexport default from dynamic */ _Typography__WEBPACK_IMPORTED_MODULE_4___default.a)\n/* harmony export */ });\n/* harmony import */ var _Box__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Box */ \"../../node_modules/@mui/material/node/Box/index.js\");\n/* harmony import */ var _Box__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Box__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Card */ \"../../node_modules/@mui/material/node/Card/index.js\");\n/* harmony import */ var _Card__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_Card__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _CardContent__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CardContent */ \"../../node_modules/@mui/material/node/CardContent/index.js\");\n/* harmony import */ var _CardContent__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_CardContent__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Chip__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Chip */ \"../../node_modules/@mui/material/node/Chip/index.js\");\n/* harmony import */ var _Chip__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_Chip__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _Typography__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Typography */ \"../../node_modules/@mui/material/node/Typography/index.js\");\n/* harmony import */ var _Typography__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_Typography__WEBPACK_IMPORTED_MODULE_4__);\n/**\n * @mui/material v5.18.0\n *\n * @license MIT\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */ /* eslint-disable import/export */ \n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1Cb3gsQ2FyZCxDYXJkQ29udGVudCxDaGlwLFR5cG9ncmFwaHkhPSEuLi8uLi9ub2RlX21vZHVsZXMvQG11aS9tYXRlcmlhbC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNzQztBQUNFO0FBQ2M7QUFDZCIsInNvdXJjZXMiOlsid2VicGFjazovL3NvbGFuYS10cmFkaW5nLWRhc2hib2FyZC8uLi8uLi9ub2RlX21vZHVsZXMvQG11aS9tYXRlcmlhbC9pbmRleC5qcz9jZWNiIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQG11aS9tYXRlcmlhbCB2NS4xOC4wXG4gKlxuICogQGxpY2Vuc2UgTUlUXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZSBmb3VuZCBpbiB0aGVcbiAqIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqLyAvKiBlc2xpbnQtZGlzYWJsZSBpbXBvcnQvZXhwb3J0ICovIFxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCb3ggfSBmcm9tIFwiLi9Cb3hcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDYXJkIH0gZnJvbSBcIi4vQ2FyZFwiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENhcmRDb250ZW50IH0gZnJvbSBcIi4vQ2FyZENvbnRlbnRcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDaGlwIH0gZnJvbSBcIi4vQ2hpcFwiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFR5cG9ncmFwaHkgfSBmcm9tIFwiLi9UeXBvZ3JhcGh5XCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Box,Card,CardContent,Chip,Typography!=!../../node_modules/@mui/material/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=Box,Card,CardContent,CircularProgress,Grid,Skeleton,Stack,Typography!=!../../node_modules/@mui/material/index.js":
/*!**************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Box,Card,CardContent,CircularProgress,Grid,Skeleton,Stack,Typography!=!../../node_modules/@mui/material/index.js ***!
  \**************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Box: () => (/* reexport default from dynamic */ _Box__WEBPACK_IMPORTED_MODULE_0___default.a),\n/* harmony export */   Card: () => (/* reexport default from dynamic */ _Card__WEBPACK_IMPORTED_MODULE_1___default.a),\n/* harmony export */   CardContent: () => (/* reexport default from dynamic */ _CardContent__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   CircularProgress: () => (/* reexport default from dynamic */ _CircularProgress__WEBPACK_IMPORTED_MODULE_3___default.a),\n/* harmony export */   Grid: () => (/* reexport default from dynamic */ _Grid__WEBPACK_IMPORTED_MODULE_4___default.a),\n/* harmony export */   Skeleton: () => (/* reexport default from dynamic */ _Skeleton__WEBPACK_IMPORTED_MODULE_5___default.a),\n/* harmony export */   Stack: () => (/* reexport safe */ _Stack__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   Typography: () => (/* reexport default from dynamic */ _Typography__WEBPACK_IMPORTED_MODULE_7___default.a)\n/* harmony export */ });\n/* harmony import */ var _Box__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Box */ \"../../node_modules/@mui/material/node/Box/index.js\");\n/* harmony import */ var _Box__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Box__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Card */ \"../../node_modules/@mui/material/node/Card/index.js\");\n/* harmony import */ var _Card__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_Card__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _CardContent__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CardContent */ \"../../node_modules/@mui/material/node/CardContent/index.js\");\n/* harmony import */ var _CardContent__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_CardContent__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _CircularProgress__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CircularProgress */ \"../../node_modules/@mui/material/node/CircularProgress/index.js\");\n/* harmony import */ var _CircularProgress__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_CircularProgress__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _Grid__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Grid */ \"../../node_modules/@mui/material/node/Grid/index.js\");\n/* harmony import */ var _Grid__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_Grid__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _Skeleton__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Skeleton */ \"../../node_modules/@mui/material/node/Skeleton/index.js\");\n/* harmony import */ var _Skeleton__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_Skeleton__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _Stack__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Stack */ \"../../node_modules/@mui/material/node/Stack/index.js\");\n/* harmony import */ var _Typography__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./Typography */ \"../../node_modules/@mui/material/node/Typography/index.js\");\n/* harmony import */ var _Typography__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_Typography__WEBPACK_IMPORTED_MODULE_7__);\n/**\n * @mui/material v5.18.0\n *\n * @license MIT\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */ /* eslint-disable import/export */ \n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1Cb3gsQ2FyZCxDYXJkQ29udGVudCxDaXJjdWxhclByb2dyZXNzLEdyaWQsU2tlbGV0b24sU3RhY2ssVHlwb2dyYXBoeSE9IS4uLy4uL25vZGVfbW9kdWxlcy9AbXVpL21hdGVyaWFsL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDc0M7QUFDRTtBQUNjO0FBQ1U7QUFDeEI7QUFDUTtBQUNOIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc29sYW5hLXRyYWRpbmctZGFzaGJvYXJkLy4uLy4uL25vZGVfbW9kdWxlcy9AbXVpL21hdGVyaWFsL2luZGV4LmpzPzg3NGUiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbXVpL21hdGVyaWFsIHY1LjE4LjBcbiAqXG4gKiBAbGljZW5zZSBNSVRcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIE1JVCBsaWNlbnNlIGZvdW5kIGluIHRoZVxuICogTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICovIC8qIGVzbGludC1kaXNhYmxlIGltcG9ydC9leHBvcnQgKi8gXG5leHBvcnQgeyBkZWZhdWx0IGFzIEJveCB9IGZyb20gXCIuL0JveFwiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENhcmQgfSBmcm9tIFwiLi9DYXJkXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2FyZENvbnRlbnQgfSBmcm9tIFwiLi9DYXJkQ29udGVudFwiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENpcmN1bGFyUHJvZ3Jlc3MgfSBmcm9tIFwiLi9DaXJjdWxhclByb2dyZXNzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgR3JpZCB9IGZyb20gXCIuL0dyaWRcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTa2VsZXRvbiB9IGZyb20gXCIuL1NrZWxldG9uXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgU3RhY2sgfSBmcm9tIFwiLi9TdGFja1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFR5cG9ncmFwaHkgfSBmcm9tIFwiLi9UeXBvZ3JhcGh5XCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Box,Card,CardContent,CircularProgress,Grid,Skeleton,Stack,Typography!=!../../node_modules/@mui/material/index.js\n");

/***/ }),

/***/ "../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"../../node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"../../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./pages/_document.tsx\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n/* harmony import */ var _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\index.tsx */ \"./pages/index.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__, private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__, private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/ClientOnly.tsx":
/*!***********************************!*\
  !*** ./components/ClientOnly.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst ClientOnly = ({ children, fallback = null })=>{\n    const [hasMounted, setHasMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setHasMounted(true);\n    }, []);\n    if (!hasMounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: fallback\n        }, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ClientOnly);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0NsaWVudE9ubHkudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFtRDtBQU9uRCxNQUFNRyxhQUF3QyxDQUFDLEVBQUVDLFFBQVEsRUFBRUMsV0FBVyxJQUFJLEVBQUU7SUFDMUUsTUFBTSxDQUFDQyxZQUFZQyxjQUFjLEdBQUdOLCtDQUFRQSxDQUFDO0lBRTdDQyxnREFBU0EsQ0FBQztRQUNSSyxjQUFjO0lBQ2hCLEdBQUcsRUFBRTtJQUVMLElBQUksQ0FBQ0QsWUFBWTtRQUNmLHFCQUFPO3NCQUFHRDs7SUFDWjtJQUVBLHFCQUFPO2tCQUFHRDs7QUFDWjtBQUVBLGlFQUFlRCxVQUFVQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc29sYW5hLXRyYWRpbmctZGFzaGJvYXJkLy4vY29tcG9uZW50cy9DbGllbnRPbmx5LnRzeD8wNzUzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuXG5pbnRlcmZhY2UgQ2xpZW50T25seVByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbiAgZmFsbGJhY2s/OiBSZWFjdC5SZWFjdE5vZGU7XG59XG5cbmNvbnN0IENsaWVudE9ubHk6IFJlYWN0LkZDPENsaWVudE9ubHlQcm9wcz4gPSAoeyBjaGlsZHJlbiwgZmFsbGJhY2sgPSBudWxsIH0pID0+IHtcbiAgY29uc3QgW2hhc01vdW50ZWQsIHNldEhhc01vdW50ZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgc2V0SGFzTW91bnRlZCh0cnVlKTtcbiAgfSwgW10pO1xuXG4gIGlmICghaGFzTW91bnRlZCkge1xuICAgIHJldHVybiA8PntmYWxsYmFja308Lz47XG4gIH1cblxuICByZXR1cm4gPD57Y2hpbGRyZW59PC8+O1xufTtcblxuZXhwb3J0IGRlZmF1bHQgQ2xpZW50T25seTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiQ2xpZW50T25seSIsImNoaWxkcmVuIiwiZmFsbGJhY2siLCJoYXNNb3VudGVkIiwic2V0SGFzTW91bnRlZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/ClientOnly.tsx\n");

/***/ }),

/***/ "./components/ErrorBoundary.tsx":
/*!**************************************!*\
  !*** ./components/ErrorBoundary.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_AlertTitle_Box_Button_Card_CardContent_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,AlertTitle,Box,Button,Card,CardContent,Stack,Typography!=!@mui/material */ \"__barrel_optimize__?names=Alert,AlertTitle,Box,Button,Card,CardContent,Stack,Typography!=!../../node_modules/@mui/material/index.js\");\n/* harmony import */ var _mui_icons_material_ErrorOutline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mui/icons-material/ErrorOutline */ \"@mui/icons-material/ErrorOutline\");\n/* harmony import */ var _mui_icons_material_ErrorOutline__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_mui_icons_material_ErrorOutline__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _mui_icons_material_Refresh__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/icons-material/Refresh */ \"@mui/icons-material/Refresh\");\n/* harmony import */ var _mui_icons_material_Refresh__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_mui_icons_material_Refresh__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _mui_icons_material_Home__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/icons-material/Home */ \"@mui/icons-material/Home\");\n/* harmony import */ var _mui_icons_material_Home__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_mui_icons_material_Home__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\n\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    constructor(props){\n        super(props);\n        this.handleRetry = ()=>{\n            this.setState({\n                hasError: false,\n                error: null,\n                errorInfo: null\n            });\n        };\n        this.handleGoHome = ()=>{\n            window.location.href = \"/\";\n        };\n        this.state = {\n            hasError: false,\n            error: null,\n            errorInfo: null\n        };\n    }\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true,\n            error,\n            errorInfo: null\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        this.setState({\n            error,\n            errorInfo\n        });\n        // Log error to monitoring service\n        console.error(\"ErrorBoundary caught an error:\", error, errorInfo);\n        // Call custom error handler if provided\n        if (this.props.onError) {\n            this.props.onError(error, errorInfo);\n        }\n        // In production, send to error tracking service\n        if (false) {}\n    }\n    render() {\n        if (this.state.hasError) {\n            // Custom fallback UI\n            if (this.props.fallback) {\n                return this.props.fallback;\n            }\n            // Default error UI\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertTitle_Box_Button_Card_CardContent_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                minHeight: \"100vh\",\n                p: 3,\n                bgcolor: \"background.default\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertTitle_Box_Button_Card_CardContent_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    sx: {\n                        maxWidth: 600,\n                        width: \"100%\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertTitle_Box_Button_Card_CardContent_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertTitle_Box_Button_Card_CardContent_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__.Stack, {\n                            spacing: 3,\n                            alignItems: \"center\",\n                            textAlign: \"center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_icons_material_ErrorOutline__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    color: \"error\",\n                                    sx: {\n                                        fontSize: 64\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertTitle_Box_Button_Card_CardContent_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__.Typography, {\n                                    variant: \"h4\",\n                                    component: \"h1\",\n                                    color: \"error\",\n                                    children: \"Oops! Something went wrong\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertTitle_Box_Button_Card_CardContent_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__.Typography, {\n                                    variant: \"body1\",\n                                    color: \"text.secondary\",\n                                    children: \"We're sorry, but something unexpected happened. Our team has been notified and is working to fix the issue.\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 17\n                                }, this),\n                                 true && this.state.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertTitle_Box_Button_Card_CardContent_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                    severity: \"error\",\n                                    sx: {\n                                        width: \"100%\",\n                                        textAlign: \"left\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertTitle_Box_Button_Card_CardContent_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__.AlertTitle, {\n                                            children: \"Error Details (Development Mode)\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertTitle_Box_Button_Card_CardContent_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__.Typography, {\n                                            variant: \"body2\",\n                                            component: \"pre\",\n                                            sx: {\n                                                whiteSpace: \"pre-wrap\",\n                                                fontSize: \"0.75rem\",\n                                                fontFamily: \"monospace\",\n                                                mt: 1\n                                            },\n                                            children: [\n                                                this.state.error.toString(),\n                                                this.state.errorInfo?.componentStack\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertTitle_Box_Button_Card_CardContent_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__.Stack, {\n                                    direction: \"row\",\n                                    spacing: 2,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertTitle_Box_Button_Card_CardContent_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"contained\",\n                                            startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_icons_material_Refresh__WEBPACK_IMPORTED_MODULE_3___default()), {}, void 0, false, void 0, void 0),\n                                            onClick: this.handleRetry,\n                                            color: \"primary\",\n                                            children: \"Try Again\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertTitle_Box_Button_Card_CardContent_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"outlined\",\n                                            startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_icons_material_Home__WEBPACK_IMPORTED_MODULE_4___default()), {}, void 0, false, void 0, void 0),\n                                            onClick: this.handleGoHome,\n                                            color: \"primary\",\n                                            children: \"Go Home\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertTitle_Box_Button_Card_CardContent_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__.Typography, {\n                                    variant: \"caption\",\n                                    color: \"text.secondary\",\n                                    children: [\n                                        \"Error ID: \",\n                                        Date.now().toString(36)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\ErrorBoundary.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 85,\n                columnNumber: 9\n            }, this);\n        }\n        return this.props.children;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ErrorBoundary);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0Vycm9yQm91bmRhcnkudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBK0Q7QUFVeEM7QUFDMkM7QUFBQTtBQUFBO0FBY2xFLE1BQU1hLHNCQUFzQlosNENBQVNBO0lBQ25DYSxZQUFZQyxLQUFZLENBQUU7UUFDeEIsS0FBSyxDQUFDQTthQW9DUkMsY0FBYztZQUNaLElBQUksQ0FBQ0MsUUFBUSxDQUFDO2dCQUNaQyxVQUFVO2dCQUNWQyxPQUFPO2dCQUNQQyxXQUFXO1lBQ2I7UUFDRjthQUVBQyxlQUFlO1lBQ2JDLE9BQU9DLFFBQVEsQ0FBQ0MsSUFBSSxHQUFHO1FBQ3pCO1FBN0NFLElBQUksQ0FBQ0MsS0FBSyxHQUFHO1lBQ1hQLFVBQVU7WUFDVkMsT0FBTztZQUNQQyxXQUFXO1FBQ2I7SUFDRjtJQUVBLE9BQU9NLHlCQUF5QlAsS0FBWSxFQUFTO1FBQ25ELE9BQU87WUFDTEQsVUFBVTtZQUNWQztZQUNBQyxXQUFXO1FBQ2I7SUFDRjtJQUVBTyxrQkFBa0JSLEtBQVksRUFBRUMsU0FBb0IsRUFBRTtRQUNwRCxJQUFJLENBQUNILFFBQVEsQ0FBQztZQUNaRTtZQUNBQztRQUNGO1FBRUEsa0NBQWtDO1FBQ2xDUSxRQUFRVCxLQUFLLENBQUMsa0NBQWtDQSxPQUFPQztRQUV2RCx3Q0FBd0M7UUFDeEMsSUFBSSxJQUFJLENBQUNMLEtBQUssQ0FBQ2MsT0FBTyxFQUFFO1lBQ3RCLElBQUksQ0FBQ2QsS0FBSyxDQUFDYyxPQUFPLENBQUNWLE9BQU9DO1FBQzVCO1FBRUEsZ0RBQWdEO1FBQ2hELElBQUlVLEtBQXlCLEVBQWMsRUFFMUM7SUFDSDtJQWNBQyxTQUFTO1FBQ1AsSUFBSSxJQUFJLENBQUNOLEtBQUssQ0FBQ1AsUUFBUSxFQUFFO1lBQ3ZCLHFCQUFxQjtZQUNyQixJQUFJLElBQUksQ0FBQ0gsS0FBSyxDQUFDaUIsUUFBUSxFQUFFO2dCQUN2QixPQUFPLElBQUksQ0FBQ2pCLEtBQUssQ0FBQ2lCLFFBQVE7WUFDNUI7WUFFQSxtQkFBbUI7WUFDbkIscUJBQ0UsOERBQUM5QixrSUFBR0E7Z0JBQ0YrQixTQUFRO2dCQUNSQyxnQkFBZTtnQkFDZkMsWUFBVztnQkFDWEMsV0FBVTtnQkFDVkMsR0FBRztnQkFDSEMsU0FBUTswQkFFUiw0RUFBQ2pDLG1JQUFJQTtvQkFBQ2tDLElBQUk7d0JBQUVDLFVBQVU7d0JBQUtDLE9BQU87b0JBQU87OEJBQ3ZDLDRFQUFDbkMsMElBQVdBO2tDQUNWLDRFQUFDRyxvSUFBS0E7NEJBQUNpQyxTQUFTOzRCQUFHUCxZQUFXOzRCQUFTUSxXQUFVOzs4Q0FDL0MsOERBQUNqQyx5RUFBWUE7b0NBQUNrQyxPQUFNO29DQUFRTCxJQUFJO3dDQUFFTSxVQUFVO29DQUFHOzs7Ozs7OENBRS9DLDhEQUFDMUMseUlBQVVBO29DQUFDMkMsU0FBUTtvQ0FBS0MsV0FBVTtvQ0FBS0gsT0FBTTs4Q0FBUTs7Ozs7OzhDQUl0RCw4REFBQ3pDLHlJQUFVQTtvQ0FBQzJDLFNBQVE7b0NBQVFGLE9BQU07OENBQWlCOzs7Ozs7Z0NBckduRSxLQXlHMEMsSUFBaUIsSUFBSSxDQUFDbkIsS0FBSyxDQUFDTixLQUFLLGtCQUN6RCw4REFBQ1osb0lBQUtBO29DQUFDeUMsVUFBUztvQ0FBUVQsSUFBSTt3Q0FBRUUsT0FBTzt3Q0FBUUUsV0FBVztvQ0FBTzs7c0RBQzdELDhEQUFDbkMseUlBQVVBO3NEQUFDOzs7Ozs7c0RBQ1osOERBQUNMLHlJQUFVQTs0Q0FBQzJDLFNBQVE7NENBQVFDLFdBQVU7NENBQU1SLElBQUk7Z0RBQzlDVSxZQUFZO2dEQUNaSixVQUFVO2dEQUNWSyxZQUFZO2dEQUNaQyxJQUFJOzRDQUNOOztnREFDRyxJQUFJLENBQUMxQixLQUFLLENBQUNOLEtBQUssQ0FBQ2lDLFFBQVE7Z0RBQ3pCLElBQUksQ0FBQzNCLEtBQUssQ0FBQ0wsU0FBUyxFQUFFaUM7Ozs7Ozs7Ozs7Ozs7OENBSzdCLDhEQUFDNUMsb0lBQUtBO29DQUFDNkMsV0FBVTtvQ0FBTVosU0FBUzs7c0RBQzlCLDhEQUFDdEMscUlBQU1BOzRDQUNMMEMsU0FBUTs0Q0FDUlMseUJBQVcsOERBQUM1QyxvRUFBT0E7NENBQ25CNkMsU0FBUyxJQUFJLENBQUN4QyxXQUFXOzRDQUN6QjRCLE9BQU07c0RBQ1A7Ozs7OztzREFJRCw4REFBQ3hDLHFJQUFNQTs0Q0FDTDBDLFNBQVE7NENBQ1JTLHlCQUFXLDhEQUFDM0MsaUVBQUlBOzRDQUNoQjRDLFNBQVMsSUFBSSxDQUFDbkMsWUFBWTs0Q0FDMUJ1QixPQUFNO3NEQUNQOzs7Ozs7Ozs7Ozs7OENBS0gsOERBQUN6Qyx5SUFBVUE7b0NBQUMyQyxTQUFRO29DQUFVRixPQUFNOzt3Q0FBaUI7d0NBQ3hDYSxLQUFLQyxHQUFHLEdBQUdOLFFBQVEsQ0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztRQU83QztRQUVBLE9BQU8sSUFBSSxDQUFDckMsS0FBSyxDQUFDNEMsUUFBUTtJQUM1QjtBQUNGO0FBRUEsaUVBQWU5QyxhQUFhQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc29sYW5hLXRyYWRpbmctZGFzaGJvYXJkLy4vY29tcG9uZW50cy9FcnJvckJvdW5kYXJ5LnRzeD9lMzBlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCwgeyBDb21wb25lbnQsIEVycm9ySW5mbywgUmVhY3ROb2RlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHtcbiAgQm94LFxuICBUeXBvZ3JhcGh5LFxuICBCdXR0b24sXG4gIENhcmQsXG4gIENhcmRDb250ZW50LFxuICBBbGVydCxcbiAgQWxlcnRUaXRsZSxcbiAgU3RhY2ssXG59IGZyb20gJ0BtdWkvbWF0ZXJpYWwnO1xuaW1wb3J0IHsgRXJyb3JPdXRsaW5lLCBSZWZyZXNoLCBIb21lIH0gZnJvbSAnQG11aS9pY29ucy1tYXRlcmlhbCc7XG5cbmludGVyZmFjZSBQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdE5vZGU7XG4gIGZhbGxiYWNrPzogUmVhY3ROb2RlO1xuICBvbkVycm9yPzogKGVycm9yOiBFcnJvciwgZXJyb3JJbmZvOiBFcnJvckluZm8pID0+IHZvaWQ7XG59XG5cbmludGVyZmFjZSBTdGF0ZSB7XG4gIGhhc0Vycm9yOiBib29sZWFuO1xuICBlcnJvcjogRXJyb3IgfCBudWxsO1xuICBlcnJvckluZm86IEVycm9ySW5mbyB8IG51bGw7XG59XG5cbmNsYXNzIEVycm9yQm91bmRhcnkgZXh0ZW5kcyBDb21wb25lbnQ8UHJvcHMsIFN0YXRlPiB7XG4gIGNvbnN0cnVjdG9yKHByb3BzOiBQcm9wcykge1xuICAgIHN1cGVyKHByb3BzKTtcbiAgICB0aGlzLnN0YXRlID0ge1xuICAgICAgaGFzRXJyb3I6IGZhbHNlLFxuICAgICAgZXJyb3I6IG51bGwsXG4gICAgICBlcnJvckluZm86IG51bGwsXG4gICAgfTtcbiAgfVxuXG4gIHN0YXRpYyBnZXREZXJpdmVkU3RhdGVGcm9tRXJyb3IoZXJyb3I6IEVycm9yKTogU3RhdGUge1xuICAgIHJldHVybiB7XG4gICAgICBoYXNFcnJvcjogdHJ1ZSxcbiAgICAgIGVycm9yLFxuICAgICAgZXJyb3JJbmZvOiBudWxsLFxuICAgIH07XG4gIH1cblxuICBjb21wb25lbnREaWRDYXRjaChlcnJvcjogRXJyb3IsIGVycm9ySW5mbzogRXJyb3JJbmZvKSB7XG4gICAgdGhpcy5zZXRTdGF0ZSh7XG4gICAgICBlcnJvcixcbiAgICAgIGVycm9ySW5mbyxcbiAgICB9KTtcblxuICAgIC8vIExvZyBlcnJvciB0byBtb25pdG9yaW5nIHNlcnZpY2VcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvckJvdW5kYXJ5IGNhdWdodCBhbiBlcnJvcjonLCBlcnJvciwgZXJyb3JJbmZvKTtcbiAgICBcbiAgICAvLyBDYWxsIGN1c3RvbSBlcnJvciBoYW5kbGVyIGlmIHByb3ZpZGVkXG4gICAgaWYgKHRoaXMucHJvcHMub25FcnJvcikge1xuICAgICAgdGhpcy5wcm9wcy5vbkVycm9yKGVycm9yLCBlcnJvckluZm8pO1xuICAgIH1cblxuICAgIC8vIEluIHByb2R1Y3Rpb24sIHNlbmQgdG8gZXJyb3IgdHJhY2tpbmcgc2VydmljZVxuICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gICAgICAvLyBFeGFtcGxlOiBTZW50cnkuY2FwdHVyZUV4Y2VwdGlvbihlcnJvciwgeyBleHRyYTogZXJyb3JJbmZvIH0pO1xuICAgIH1cbiAgfVxuXG4gIGhhbmRsZVJldHJ5ID0gKCkgPT4ge1xuICAgIHRoaXMuc2V0U3RhdGUoe1xuICAgICAgaGFzRXJyb3I6IGZhbHNlLFxuICAgICAgZXJyb3I6IG51bGwsXG4gICAgICBlcnJvckluZm86IG51bGwsXG4gICAgfSk7XG4gIH07XG5cbiAgaGFuZGxlR29Ib21lID0gKCkgPT4ge1xuICAgIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gJy8nO1xuICB9O1xuXG4gIHJlbmRlcigpIHtcbiAgICBpZiAodGhpcy5zdGF0ZS5oYXNFcnJvcikge1xuICAgICAgLy8gQ3VzdG9tIGZhbGxiYWNrIFVJXG4gICAgICBpZiAodGhpcy5wcm9wcy5mYWxsYmFjaykge1xuICAgICAgICByZXR1cm4gdGhpcy5wcm9wcy5mYWxsYmFjaztcbiAgICAgIH1cblxuICAgICAgLy8gRGVmYXVsdCBlcnJvciBVSVxuICAgICAgcmV0dXJuIChcbiAgICAgICAgPEJveFxuICAgICAgICAgIGRpc3BsYXk9XCJmbGV4XCJcbiAgICAgICAgICBqdXN0aWZ5Q29udGVudD1cImNlbnRlclwiXG4gICAgICAgICAgYWxpZ25JdGVtcz1cImNlbnRlclwiXG4gICAgICAgICAgbWluSGVpZ2h0PVwiMTAwdmhcIlxuICAgICAgICAgIHA9ezN9XG4gICAgICAgICAgYmdjb2xvcj1cImJhY2tncm91bmQuZGVmYXVsdFwiXG4gICAgICAgID5cbiAgICAgICAgICA8Q2FyZCBzeD17eyBtYXhXaWR0aDogNjAwLCB3aWR0aDogJzEwMCUnIH19PlxuICAgICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgICA8U3RhY2sgc3BhY2luZz17M30gYWxpZ25JdGVtcz1cImNlbnRlclwiIHRleHRBbGlnbj1cImNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxFcnJvck91dGxpbmUgY29sb3I9XCJlcnJvclwiIHN4PXt7IGZvbnRTaXplOiA2NCB9fSAvPlxuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJoNFwiIGNvbXBvbmVudD1cImgxXCIgY29sb3I9XCJlcnJvclwiPlxuICAgICAgICAgICAgICAgICAgT29wcyEgU29tZXRoaW5nIHdlbnQgd3JvbmdcbiAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImJvZHkxXCIgY29sb3I9XCJ0ZXh0LnNlY29uZGFyeVwiPlxuICAgICAgICAgICAgICAgICAgV2UncmUgc29ycnksIGJ1dCBzb21ldGhpbmcgdW5leHBlY3RlZCBoYXBwZW5lZC4gT3VyIHRlYW0gaGFzIGJlZW4gbm90aWZpZWQgYW5kIGlzIHdvcmtpbmcgdG8gZml4IHRoZSBpc3N1ZS5cbiAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XG5cbiAgICAgICAgICAgICAgICB7cHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcgJiYgdGhpcy5zdGF0ZS5lcnJvciAmJiAoXG4gICAgICAgICAgICAgICAgICA8QWxlcnQgc2V2ZXJpdHk9XCJlcnJvclwiIHN4PXt7IHdpZHRoOiAnMTAwJScsIHRleHRBbGlnbjogJ2xlZnQnIH19PlxuICAgICAgICAgICAgICAgICAgICA8QWxlcnRUaXRsZT5FcnJvciBEZXRhaWxzIChEZXZlbG9wbWVudCBNb2RlKTwvQWxlcnRUaXRsZT5cbiAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImJvZHkyXCIgY29tcG9uZW50PVwicHJlXCIgc3g9e3sgXG4gICAgICAgICAgICAgICAgICAgICAgd2hpdGVTcGFjZTogJ3ByZS13cmFwJyxcbiAgICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogJzAuNzVyZW0nLFxuICAgICAgICAgICAgICAgICAgICAgIGZvbnRGYW1pbHk6ICdtb25vc3BhY2UnLFxuICAgICAgICAgICAgICAgICAgICAgIG10OiAxXG4gICAgICAgICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgICAgICAgIHt0aGlzLnN0YXRlLmVycm9yLnRvU3RyaW5nKCl9XG4gICAgICAgICAgICAgICAgICAgICAge3RoaXMuc3RhdGUuZXJyb3JJbmZvPy5jb21wb25lbnRTdGFja31cbiAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxuICAgICAgICAgICAgICAgICAgPC9BbGVydD5cbiAgICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgICAgPFN0YWNrIGRpcmVjdGlvbj1cInJvd1wiIHNwYWNpbmc9ezJ9PlxuICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiY29udGFpbmVkXCJcbiAgICAgICAgICAgICAgICAgICAgc3RhcnRJY29uPXs8UmVmcmVzaCAvPn1cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17dGhpcy5oYW5kbGVSZXRyeX1cbiAgICAgICAgICAgICAgICAgICAgY29sb3I9XCJwcmltYXJ5XCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgVHJ5IEFnYWluXG4gICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZWRcIlxuICAgICAgICAgICAgICAgICAgICBzdGFydEljb249ezxIb21lIC8+fVxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXt0aGlzLmhhbmRsZUdvSG9tZX1cbiAgICAgICAgICAgICAgICAgICAgY29sb3I9XCJwcmltYXJ5XCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgR28gSG9tZVxuICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgPC9TdGFjaz5cblxuICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJjYXB0aW9uXCIgY29sb3I9XCJ0ZXh0LnNlY29uZGFyeVwiPlxuICAgICAgICAgICAgICAgICAgRXJyb3IgSUQ6IHtEYXRlLm5vdygpLnRvU3RyaW5nKDM2KX1cbiAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgICAgICAgIDwvU3RhY2s+XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgPC9Cb3g+XG4gICAgICApO1xuICAgIH1cblxuICAgIHJldHVybiB0aGlzLnByb3BzLmNoaWxkcmVuO1xuICB9XG59XG5cbmV4cG9ydCBkZWZhdWx0IEVycm9yQm91bmRhcnk7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJDb21wb25lbnQiLCJCb3giLCJUeXBvZ3JhcGh5IiwiQnV0dG9uIiwiQ2FyZCIsIkNhcmRDb250ZW50IiwiQWxlcnQiLCJBbGVydFRpdGxlIiwiU3RhY2siLCJFcnJvck91dGxpbmUiLCJSZWZyZXNoIiwiSG9tZSIsIkVycm9yQm91bmRhcnkiLCJjb25zdHJ1Y3RvciIsInByb3BzIiwiaGFuZGxlUmV0cnkiLCJzZXRTdGF0ZSIsImhhc0Vycm9yIiwiZXJyb3IiLCJlcnJvckluZm8iLCJoYW5kbGVHb0hvbWUiLCJ3aW5kb3ciLCJsb2NhdGlvbiIsImhyZWYiLCJzdGF0ZSIsImdldERlcml2ZWRTdGF0ZUZyb21FcnJvciIsImNvbXBvbmVudERpZENhdGNoIiwiY29uc29sZSIsIm9uRXJyb3IiLCJwcm9jZXNzIiwicmVuZGVyIiwiZmFsbGJhY2siLCJkaXNwbGF5IiwianVzdGlmeUNvbnRlbnQiLCJhbGlnbkl0ZW1zIiwibWluSGVpZ2h0IiwicCIsImJnY29sb3IiLCJzeCIsIm1heFdpZHRoIiwid2lkdGgiLCJzcGFjaW5nIiwidGV4dEFsaWduIiwiY29sb3IiLCJmb250U2l6ZSIsInZhcmlhbnQiLCJjb21wb25lbnQiLCJzZXZlcml0eSIsIndoaXRlU3BhY2UiLCJmb250RmFtaWx5IiwibXQiLCJ0b1N0cmluZyIsImNvbXBvbmVudFN0YWNrIiwiZGlyZWN0aW9uIiwic3RhcnRJY29uIiwib25DbGljayIsIkRhdGUiLCJub3ciLCJjaGlsZHJlbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/ErrorBoundary.tsx\n");

/***/ }),

/***/ "./components/Layout.tsx":
/*!*******************************!*\
  !*** ./components/Layout.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_AppBar_Box_Chip_Container_IconButton_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Box,Chip,Container,IconButton,Toolbar,Typography!=!@mui/material */ \"__barrel_optimize__?names=AppBar,Box,Chip,Container,IconButton,Toolbar,Typography!=!../../node_modules/@mui/material/index.js\");\n/* harmony import */ var _mui_icons_material_Menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/icons-material/Menu */ \"@mui/icons-material/Menu\");\n/* harmony import */ var _mui_icons_material_Menu__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_mui_icons_material_Menu__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _mui_icons_material_Notifications__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/icons-material/Notifications */ \"@mui/icons-material/Notifications\");\n/* harmony import */ var _mui_icons_material_Notifications__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_mui_icons_material_Notifications__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _mui_icons_material_AccountCircle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mui/icons-material/AccountCircle */ \"@mui/icons-material/AccountCircle\");\n/* harmony import */ var _mui_icons_material_AccountCircle__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_mui_icons_material_AccountCircle__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _mui_icons_material_TrendingUp__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mui/icons-material/TrendingUp */ \"@mui/icons-material/TrendingUp\");\n/* harmony import */ var _mui_icons_material_TrendingUp__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_mui_icons_material_TrendingUp__WEBPACK_IMPORTED_MODULE_6__);\n\n\n\n\n\n\n\n\nconst Layout = ({ children, title = \"Solana Trading Intelligence\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\Layout.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Real-time trading intelligence for Solana blockchain\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\Layout.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\Layout.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\Layout.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\Layout.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Box_Chip_Container_IconButton_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__.AppBar, {\n                position: \"static\",\n                elevation: 1,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Box_Chip_Container_IconButton_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__.Toolbar, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Box_Chip_Container_IconButton_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__.IconButton, {\n                            size: \"large\",\n                            edge: \"start\",\n                            color: \"inherit\",\n                            \"aria-label\": \"menu\",\n                            sx: {\n                                mr: 2\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_icons_material_Menu__WEBPACK_IMPORTED_MODULE_3___default()), {}, void 0, false, {\n                                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\Layout.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\Layout.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_icons_material_TrendingUp__WEBPACK_IMPORTED_MODULE_6___default()), {\n                            sx: {\n                                mr: 1\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\Layout.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Box_Chip_Container_IconButton_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__.Typography, {\n                            variant: \"h6\",\n                            component: \"div\",\n                            sx: {\n                                flexGrow: 1\n                            },\n                            children: \"Solana Trading Intelligence\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\Layout.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Box_Chip_Container_IconButton_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__.Chip, {\n                            label: \"LIVE\",\n                            color: \"success\",\n                            size: \"small\",\n                            sx: {\n                                mr: 2\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\Layout.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Box_Chip_Container_IconButton_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__.IconButton, {\n                            size: \"large\",\n                            color: \"inherit\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_icons_material_Notifications__WEBPACK_IMPORTED_MODULE_4___default()), {}, void 0, false, {\n                                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\Layout.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\Layout.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Box_Chip_Container_IconButton_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__.IconButton, {\n                            size: \"large\",\n                            color: \"inherit\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_icons_material_AccountCircle__WEBPACK_IMPORTED_MODULE_5___default()), {}, void 0, false, {\n                                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\Layout.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\Layout.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\Layout.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\Layout.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Box_Chip_Container_IconButton_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__.Container, {\n                maxWidth: \"xl\",\n                sx: {\n                    mt: 2,\n                    mb: 4\n                },\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\Layout.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Box_Chip_Container_IconButton_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__.Box, {\n                component: \"footer\",\n                sx: {\n                    py: 3,\n                    px: 2,\n                    mt: \"auto\",\n                    backgroundColor: (theme)=>theme.palette.mode === \"light\" ? theme.palette.grey[200] : theme.palette.grey[800]\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Box_Chip_Container_IconButton_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__.Container, {\n                    maxWidth: \"xl\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Box_Chip_Container_IconButton_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__.Typography, {\n                        variant: \"body2\",\n                        color: \"text.secondary\",\n                        align: \"center\",\n                        children: \"\\xa9 2024 Solana Trading Intelligence. Built with ❤️ for the Solana ecosystem.\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\Layout.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\Layout.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\Layout.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Layout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Layout.tsx\n");

/***/ }),

/***/ "./components/LoadingFallback.tsx":
/*!****************************************!*\
  !*** ./components/LoadingFallback.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_Card_CardContent_CircularProgress_Grid_Skeleton_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,CardContent,CircularProgress,Grid,Skeleton,Stack,Typography!=!@mui/material */ \"__barrel_optimize__?names=Box,Card,CardContent,CircularProgress,Grid,Skeleton,Stack,Typography!=!../../node_modules/@mui/material/index.js\");\n/* harmony import */ var _mui_icons_material_TrendingUp__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mui/icons-material/TrendingUp */ \"@mui/icons-material/TrendingUp\");\n/* harmony import */ var _mui_icons_material_TrendingUp__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_mui_icons_material_TrendingUp__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\nconst LoadingFallback = ({ type = \"component\", message = \"Loading...\", height = \"auto\" })=>{\n    if (type === \"minimal\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CircularProgress_Grid_Skeleton_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Box, {\n            display: \"flex\",\n            justifyContent: \"center\",\n            alignItems: \"center\",\n            p: 2,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CircularProgress_Grid_Skeleton_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.CircularProgress, {\n                size: 24\n            }, void 0, false, {\n                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\LoadingFallback.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\LoadingFallback.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (type === \"page\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CircularProgress_Grid_Skeleton_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Box, {\n            display: \"flex\",\n            flexDirection: \"column\",\n            justifyContent: \"center\",\n            alignItems: \"center\",\n            minHeight: \"100vh\",\n            bgcolor: \"background.default\",\n            p: 3,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CircularProgress_Grid_Skeleton_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Stack, {\n                spacing: 3,\n                alignItems: \"center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CircularProgress_Grid_Skeleton_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                        position: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_icons_material_TrendingUp__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                sx: {\n                                    fontSize: 48,\n                                    color: \"primary.main\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\LoadingFallback.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CircularProgress_Grid_Skeleton_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.CircularProgress, {\n                                size: 64,\n                                sx: {\n                                    position: \"absolute\",\n                                    top: -8,\n                                    left: -8,\n                                    color: \"primary.light\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\LoadingFallback.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\LoadingFallback.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CircularProgress_Grid_Skeleton_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                        variant: \"h5\",\n                        color: \"primary\",\n                        children: \"Solana Trading Intelligence\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\LoadingFallback.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CircularProgress_Grid_Skeleton_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                        variant: \"body1\",\n                        color: \"text.secondary\",\n                        children: message\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\LoadingFallback.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CircularProgress_Grid_Skeleton_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                        width: \"200px\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CircularProgress_Grid_Skeleton_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Skeleton, {\n                                variant: \"text\",\n                                height: 20\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\LoadingFallback.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CircularProgress_Grid_Skeleton_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Skeleton, {\n                                variant: \"text\",\n                                height: 20,\n                                width: \"60%\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\LoadingFallback.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\LoadingFallback.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\LoadingFallback.tsx\",\n                lineNumber: 44,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\LoadingFallback.tsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (type === \"chart\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CircularProgress_Grid_Skeleton_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            sx: {\n                height\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CircularProgress_Grid_Skeleton_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CircularProgress_Grid_Skeleton_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Stack, {\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CircularProgress_Grid_Skeleton_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                            display: \"flex\",\n                            justifyContent: \"space-between\",\n                            alignItems: \"center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CircularProgress_Grid_Skeleton_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Skeleton, {\n                                    variant: \"text\",\n                                    width: 150,\n                                    height: 24\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\LoadingFallback.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CircularProgress_Grid_Skeleton_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Skeleton, {\n                                    variant: \"rectangular\",\n                                    width: 80,\n                                    height: 32\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\LoadingFallback.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\LoadingFallback.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CircularProgress_Grid_Skeleton_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Skeleton, {\n                            variant: \"rectangular\",\n                            width: \"100%\",\n                            height: 300\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\LoadingFallback.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CircularProgress_Grid_Skeleton_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                            display: \"flex\",\n                            justifyContent: \"space-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CircularProgress_Grid_Skeleton_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Skeleton, {\n                                    variant: \"text\",\n                                    width: 100,\n                                    height: 20\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\LoadingFallback.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CircularProgress_Grid_Skeleton_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Skeleton, {\n                                    variant: \"text\",\n                                    width: 100,\n                                    height: 20\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\LoadingFallback.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\LoadingFallback.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\LoadingFallback.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\LoadingFallback.tsx\",\n                lineNumber: 78,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\LoadingFallback.tsx\",\n            lineNumber: 77,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (type === \"table\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CircularProgress_Grid_Skeleton_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            sx: {\n                height\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CircularProgress_Grid_Skeleton_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CircularProgress_Grid_Skeleton_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Stack, {\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CircularProgress_Grid_Skeleton_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                            display: \"flex\",\n                            justifyContent: \"space-between\",\n                            alignItems: \"center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CircularProgress_Grid_Skeleton_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Skeleton, {\n                                    variant: \"text\",\n                                    width: 200,\n                                    height: 28\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\LoadingFallback.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CircularProgress_Grid_Skeleton_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Skeleton, {\n                                    variant: \"rectangular\",\n                                    width: 120,\n                                    height: 36\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\LoadingFallback.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\LoadingFallback.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CircularProgress_Grid_Skeleton_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Grid, {\n                            container: true,\n                            spacing: 2,\n                            children: [\n                                1,\n                                2,\n                                3,\n                                4,\n                                5\n                            ].map((col)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CircularProgress_Grid_Skeleton_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Grid, {\n                                    item: true,\n                                    xs: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CircularProgress_Grid_Skeleton_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Skeleton, {\n                                        variant: \"text\",\n                                        height: 24\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\LoadingFallback.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, col, false, {\n                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\LoadingFallback.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\LoadingFallback.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 13\n                        }, undefined),\n                        [\n                            1,\n                            2,\n                            3,\n                            4,\n                            5,\n                            6,\n                            7,\n                            8\n                        ].map((row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CircularProgress_Grid_Skeleton_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Grid, {\n                                container: true,\n                                spacing: 2,\n                                children: [\n                                    1,\n                                    2,\n                                    3,\n                                    4,\n                                    5\n                                ].map((col)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CircularProgress_Grid_Skeleton_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Grid, {\n                                        item: true,\n                                        xs: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CircularProgress_Grid_Skeleton_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Skeleton, {\n                                            variant: \"text\",\n                                            height: 20\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\LoadingFallback.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, col, false, {\n                                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\LoadingFallback.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, row, false, {\n                                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\LoadingFallback.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 15\n                            }, undefined))\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\LoadingFallback.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\LoadingFallback.tsx\",\n                lineNumber: 100,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\LoadingFallback.tsx\",\n            lineNumber: 99,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Default component loading\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CircularProgress_Grid_Skeleton_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Box, {\n        display: \"flex\",\n        flexDirection: \"column\",\n        justifyContent: \"center\",\n        alignItems: \"center\",\n        p: 4,\n        sx: {\n            height\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CircularProgress_Grid_Skeleton_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.CircularProgress, {\n                size: 40,\n                sx: {\n                    mb: 2\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\LoadingFallback.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CircularProgress_Grid_Skeleton_Stack_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: message\n            }, void 0, false, {\n                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\LoadingFallback.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\LoadingFallback.tsx\",\n        lineNumber: 134,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LoadingFallback);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/LoadingFallback.tsx\n");

/***/ }),

/***/ "./components/MetricCard.tsx":
/*!***********************************!*\
  !*** ./components/MetricCard.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_Card_CardContent_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,CardContent,Chip,Typography!=!@mui/material */ \"__barrel_optimize__?names=Box,Card,CardContent,Chip,Typography!=!../../node_modules/@mui/material/index.js\");\n/* harmony import */ var _mui_icons_material_TrendingUp__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mui/icons-material/TrendingUp */ \"@mui/icons-material/TrendingUp\");\n/* harmony import */ var _mui_icons_material_TrendingUp__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_mui_icons_material_TrendingUp__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _mui_icons_material_TrendingDown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/icons-material/TrendingDown */ \"@mui/icons-material/TrendingDown\");\n/* harmony import */ var _mui_icons_material_TrendingDown__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_mui_icons_material_TrendingDown__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\n\nconst MetricCard = ({ title, value, icon, trend, color = \"primary\" })=>{\n    const getTrendColor = (trend)=>{\n        if (trend > 0) return \"success\";\n        if (trend < 0) return \"error\";\n        return \"default\";\n    };\n    const getTrendIcon = (trend)=>{\n        if (trend > 0) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_icons_material_TrendingUp__WEBPACK_IMPORTED_MODULE_2___default()), {\n            fontSize: \"small\"\n        }, void 0, false, {\n            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\MetricCard.tsx\",\n            lineNumber: 36,\n            columnNumber: 27\n        }, undefined);\n        if (trend < 0) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_icons_material_TrendingDown__WEBPACK_IMPORTED_MODULE_3___default()), {\n            fontSize: \"small\"\n        }, void 0, false, {\n            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\MetricCard.tsx\",\n            lineNumber: 37,\n            columnNumber: 27\n        }, undefined);\n        return null;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Card, {\n        elevation: 2,\n        sx: {\n            height: \"100%\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"space-between\",\n                    mb: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                            color: `${color}.main`,\n                            children: icon\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\MetricCard.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, undefined),\n                        trend !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Chip, {\n                            icon: getTrendIcon(trend),\n                            label: `${trend > 0 ? \"+\" : \"\"}${trend.toFixed(1)}%`,\n                            color: getTrendColor(trend),\n                            size: \"small\",\n                            variant: \"outlined\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\MetricCard.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\MetricCard.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                    variant: \"h4\",\n                    component: \"div\",\n                    fontWeight: \"bold\",\n                    mb: 1,\n                    children: value\n                }, void 0, false, {\n                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\MetricCard.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: title\n                }, void 0, false, {\n                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\MetricCard.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\MetricCard.tsx\",\n            lineNumber: 43,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\MetricCard.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MetricCard);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/MetricCard.tsx\n");

/***/ }),

/***/ "./hooks/useRealTimeData.ts":
/*!**********************************!*\
  !*** ./hooks/useRealTimeData.ts ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useRealTimeData: () => (/* binding */ useRealTimeData)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _useWebSocket__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useWebSocket */ \"./hooks/useWebSocket.ts\");\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/api */ \"./services/api.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_services_api__WEBPACK_IMPORTED_MODULE_2__]);\n_services_api__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst useRealTimeData = ()=>{\n    const { isConnected, lastMessage } = (0,_useWebSocket__WEBPACK_IMPORTED_MODULE_1__.useWebSocket)();\n    const [metrics, setMetrics] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        totalVolume24h: 125000000,\n        totalTrades24h: 45000,\n        activeWallets24h: 12500,\n        avgPnL24h: 2500,\n        suspiciousActivities: 23,\n        rugpullsDetected: 2\n    });\n    const [trades, setTrades] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [alerts, setAlerts] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // Fetch initial data\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const fetchInitialData = async ()=>{\n            try {\n                setLoading(true);\n                setError(null);\n                // Fetch initial metrics\n                const metricsResponse = await _services_api__WEBPACK_IMPORTED_MODULE_2__.apiService.metrics.getRealTime();\n                if (metricsResponse.success) {\n                    setMetrics(metricsResponse.data);\n                }\n                // Fetch recent trades\n                const tradesResponse = await _services_api__WEBPACK_IMPORTED_MODULE_2__.apiService.trades.getRecent({\n                    limit: 50\n                });\n                if (tradesResponse.success) {\n                    setTrades(tradesResponse.data);\n                }\n                // Fetch active alerts\n                const alertsResponse = await _services_api__WEBPACK_IMPORTED_MODULE_2__.apiService.alerts.getActive({\n                    limit: 20\n                });\n                if (alertsResponse.success) {\n                    setAlerts(alertsResponse.data);\n                }\n            } catch (err) {\n                console.error(\"Failed to fetch initial data:\", err);\n                setError(err.message || \"Failed to load data\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchInitialData();\n    }, []);\n    // Handle WebSocket messages\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (lastMessage) {\n            try {\n                switch(lastMessage.type){\n                    case \"metrics_update\":\n                        setMetrics(lastMessage.data);\n                        break;\n                    case \"new_trade\":\n                        setTrades((prev)=>[\n                                lastMessage.data,\n                                ...prev.slice(0, 99)\n                            ]); // Keep last 100 trades\n                        break;\n                    case \"new_alert\":\n                        setAlerts((prev)=>[\n                                lastMessage.data,\n                                ...prev.slice(0, 49)\n                            ]); // Keep last 50 alerts\n                        break;\n                    case \"alert_resolved\":\n                        setAlerts((prev)=>prev.map((alert)=>alert.id === lastMessage.data.id ? {\n                                    ...alert,\n                                    isResolved: true\n                                } : alert));\n                        break;\n                    default:\n                        break;\n                }\n            } catch (err) {\n                console.error(\"Error processing WebSocket message:\", err);\n            }\n        }\n    }, [\n        lastMessage\n    ]);\n    return {\n        metrics,\n        trades,\n        alerts,\n        isConnected,\n        loading,\n        error\n    };\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./hooks/useRealTimeData.ts\n");

/***/ }),

/***/ "./hooks/useWebSocket.ts":
/*!*******************************!*\
  !*** ./hooks/useWebSocket.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useWebSocket: () => (/* binding */ useWebSocket)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst useWebSocket = (url)=>{\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [lastMessage, setLastMessage] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isMounted, setIsMounted] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const ws = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const reconnectTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    const wsUrl = url || \"ws://localhost:3005\" || 0;\n    const connect = ()=>{\n        // Only connect if we're in the browser\n        if (true) {\n            return;\n        }\n        try {\n            ws.current = new WebSocket(wsUrl);\n            ws.current.onopen = ()=>{\n                setIsConnected(true);\n                console.log(\"WebSocket connected\");\n            };\n            ws.current.onmessage = (event)=>{\n                try {\n                    const data = JSON.parse(event.data);\n                    setLastMessage(data);\n                } catch (error) {\n                    console.error(\"Failed to parse WebSocket message:\", error);\n                }\n            };\n            ws.current.onclose = ()=>{\n                setIsConnected(false);\n                console.log(\"WebSocket disconnected\");\n                // Attempt to reconnect after 3 seconds\n                if (isMounted) {\n                    reconnectTimeoutRef.current = setTimeout(()=>{\n                        connect();\n                    }, 3000);\n                }\n            };\n            ws.current.onerror = (error)=>{\n                console.error(\"WebSocket error:\", error);\n                setIsConnected(false);\n            };\n        } catch (error) {\n            console.error(\"Failed to connect to WebSocket:\", error);\n            setIsConnected(false);\n        }\n    };\n    const disconnect = ()=>{\n        if (reconnectTimeoutRef.current) {\n            clearTimeout(reconnectTimeoutRef.current);\n        }\n        if (ws.current) {\n            ws.current.close();\n            ws.current = null;\n        }\n        setIsConnected(false);\n    };\n    const sendMessage = (message)=>{\n        if (ws.current && ws.current.readyState === WebSocket.OPEN) {\n            ws.current.send(JSON.stringify(message));\n        } else {\n            console.warn(\"WebSocket is not connected\");\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        setIsMounted(true);\n        return ()=>{\n            setIsMounted(false);\n        };\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (isMounted) {\n            connect();\n        }\n        return ()=>{\n            disconnect();\n        };\n    }, [\n        isMounted\n    ]);\n    return {\n        isConnected,\n        lastMessage,\n        sendMessage,\n        connect,\n        disconnect\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./hooks/useWebSocket.ts\n");

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MyApp)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _emotion_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @emotion/react */ \"@emotion/react\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/material/styles */ \"@mui/material/styles\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_mui_material_styles__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mui/material/CssBaseline */ \"@mui/material/CssBaseline\");\n/* harmony import */ var _mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tanstack/react-query */ \"@tanstack/react-query\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\n/* harmony import */ var _utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../utils/createEmotionCache */ \"./utils/createEmotionCache.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_emotion_react__WEBPACK_IMPORTED_MODULE_3__, _tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__, react_hot_toast__WEBPACK_IMPORTED_MODULE_7__, _utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_8__]);\n([_emotion_react__WEBPACK_IMPORTED_MODULE_3__, _tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__, react_hot_toast__WEBPACK_IMPORTED_MODULE_7__, _utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n// Client-side cache, shared for the whole session of the user in the browser.\nconst clientSideEmotionCache = (0,_utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_8__.createEmotionCache)();\n// Create a client for React Query\nconst queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__.QueryClient({\n    defaultOptions: {\n        queries: {\n            refetchOnWindowFocus: false,\n            retry: 1,\n            staleTime: 5 * 60 * 1000\n        }\n    }\n});\n// Material Design 3 inspired theme\nconst theme = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_4__.createTheme)({\n    palette: {\n        mode: \"light\",\n        primary: {\n            main: \"#1976d2\",\n            light: \"#42a5f5\",\n            dark: \"#1565c0\",\n            contrastText: \"#ffffff\"\n        },\n        secondary: {\n            main: \"#dc004e\",\n            light: \"#ff5983\",\n            dark: \"#9a0036\",\n            contrastText: \"#ffffff\"\n        },\n        success: {\n            main: \"#2e7d32\",\n            light: \"#4caf50\",\n            dark: \"#1b5e20\"\n        },\n        warning: {\n            main: \"#ed6c02\",\n            light: \"#ff9800\",\n            dark: \"#e65100\"\n        },\n        error: {\n            main: \"#d32f2f\",\n            light: \"#f44336\",\n            dark: \"#c62828\"\n        },\n        info: {\n            main: \"#0288d1\",\n            light: \"#03a9f4\",\n            dark: \"#01579b\"\n        },\n        background: {\n            default: \"#fafafa\",\n            paper: \"#ffffff\"\n        },\n        text: {\n            primary: \"rgba(0, 0, 0, 0.87)\",\n            secondary: \"rgba(0, 0, 0, 0.6)\"\n        }\n    },\n    typography: {\n        fontFamily: '\"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n        h1: {\n            fontWeight: 600,\n            fontSize: \"2.5rem\",\n            lineHeight: 1.2\n        },\n        h2: {\n            fontWeight: 600,\n            fontSize: \"2rem\",\n            lineHeight: 1.3\n        },\n        h3: {\n            fontWeight: 600,\n            fontSize: \"1.75rem\",\n            lineHeight: 1.4\n        },\n        h4: {\n            fontWeight: 600,\n            fontSize: \"1.5rem\",\n            lineHeight: 1.4\n        },\n        h5: {\n            fontWeight: 600,\n            fontSize: \"1.25rem\",\n            lineHeight: 1.5\n        },\n        h6: {\n            fontWeight: 600,\n            fontSize: \"1rem\",\n            lineHeight: 1.6\n        },\n        body1: {\n            fontSize: \"1rem\",\n            lineHeight: 1.5\n        },\n        body2: {\n            fontSize: \"0.875rem\",\n            lineHeight: 1.43\n        }\n    },\n    shape: {\n        borderRadius: 12\n    },\n    shadows: [\n        \"none\",\n        \"0px 2px 1px -1px rgba(0,0,0,0.2),0px 1px 1px 0px rgba(0,0,0,0.14),0px 1px 3px 0px rgba(0,0,0,0.12)\",\n        \"0px 3px 1px -2px rgba(0,0,0,0.2),0px 2px 2px 0px rgba(0,0,0,0.14),0px 1px 5px 0px rgba(0,0,0,0.12)\",\n        \"0px 3px 3px -2px rgba(0,0,0,0.2),0px 3px 4px 0px rgba(0,0,0,0.14),0px 1px 8px 0px rgba(0,0,0,0.12)\",\n        \"0px 2px 4px -1px rgba(0,0,0,0.2),0px 4px 5px 0px rgba(0,0,0,0.14),0px 1px 10px 0px rgba(0,0,0,0.12)\",\n        \"0px 3px 5px -1px rgba(0,0,0,0.2),0px 5px 8px 0px rgba(0,0,0,0.14),0px 1px 14px 0px rgba(0,0,0,0.12)\",\n        \"0px 3px 5px -1px rgba(0,0,0,0.2),0px 6px 10px 0px rgba(0,0,0,0.14),0px 1px 18px 0px rgba(0,0,0,0.12)\",\n        \"0px 4px 5px -2px rgba(0,0,0,0.2),0px 7px 10px 1px rgba(0,0,0,0.14),0px 2px 16px 1px rgba(0,0,0,0.12)\",\n        \"0px 5px 5px -3px rgba(0,0,0,0.2),0px 8px 10px 1px rgba(0,0,0,0.14),0px 3px 14px 2px rgba(0,0,0,0.12)\",\n        \"0px 5px 6px -3px rgba(0,0,0,0.2),0px 9px 12px 1px rgba(0,0,0,0.14),0px 3px 16px 2px rgba(0,0,0,0.12)\",\n        \"0px 6px 6px -3px rgba(0,0,0,0.2),0px 10px 14px 1px rgba(0,0,0,0.14),0px 4px 18px 3px rgba(0,0,0,0.12)\",\n        \"0px 6px 7px -4px rgba(0,0,0,0.2),0px 11px 15px 1px rgba(0,0,0,0.14),0px 4px 20px 3px rgba(0,0,0,0.12)\",\n        \"0px 7px 8px -4px rgba(0,0,0,0.2),0px 12px 17px 2px rgba(0,0,0,0.14),0px 5px 22px 4px rgba(0,0,0,0.12)\",\n        \"0px 7px 8px -4px rgba(0,0,0,0.2),0px 13px 19px 2px rgba(0,0,0,0.14),0px 5px 24px 4px rgba(0,0,0,0.12)\",\n        \"0px 7px 9px -4px rgba(0,0,0,0.2),0px 14px 21px 2px rgba(0,0,0,0.14),0px 5px 26px 4px rgba(0,0,0,0.12)\",\n        \"0px 8px 9px -5px rgba(0,0,0,0.2),0px 15px 22px 2px rgba(0,0,0,0.14),0px 6px 28px 5px rgba(0,0,0,0.12)\",\n        \"0px 8px 10px -5px rgba(0,0,0,0.2),0px 16px 24px 2px rgba(0,0,0,0.14),0px 6px 30px 5px rgba(0,0,0,0.12)\",\n        \"0px 8px 11px -5px rgba(0,0,0,0.2),0px 17px 26px 2px rgba(0,0,0,0.14),0px 6px 32px 5px rgba(0,0,0,0.12)\",\n        \"0px 9px 11px -5px rgba(0,0,0,0.2),0px 18px 28px 2px rgba(0,0,0,0.14),0px 7px 34px 6px rgba(0,0,0,0.12)\",\n        \"0px 9px 12px -6px rgba(0,0,0,0.2),0px 19px 29px 2px rgba(0,0,0,0.14),0px 7px 36px 6px rgba(0,0,0,0.12)\",\n        \"0px 10px 13px -6px rgba(0,0,0,0.2),0px 20px 31px 3px rgba(0,0,0,0.14),0px 8px 38px 7px rgba(0,0,0,0.12)\",\n        \"0px 10px 13px -6px rgba(0,0,0,0.2),0px 21px 33px 3px rgba(0,0,0,0.14),0px 8px 40px 7px rgba(0,0,0,0.12)\",\n        \"0px 10px 14px -6px rgba(0,0,0,0.2),0px 22px 35px 3px rgba(0,0,0,0.14),0px 8px 42px 7px rgba(0,0,0,0.12)\",\n        \"0px 11px 14px -7px rgba(0,0,0,0.2),0px 23px 36px 3px rgba(0,0,0,0.14),0px 9px 44px 8px rgba(0,0,0,0.12)\",\n        \"0px 11px 15px -7px rgba(0,0,0,0.2),0px 24px 38px 3px rgba(0,0,0,0.14),0px 9px 46px 8px rgba(0,0,0,0.12)\"\n    ],\n    components: {\n        MuiCard: {\n            styleOverrides: {\n                root: {\n                    borderRadius: 16,\n                    boxShadow: \"0px 2px 8px rgba(0, 0, 0, 0.1)\",\n                    transition: \"box-shadow 0.3s ease-in-out, transform 0.2s ease-in-out\",\n                    \"&:hover\": {\n                        boxShadow: \"0px 4px 16px rgba(0, 0, 0, 0.15)\",\n                        transform: \"translateY(-2px)\"\n                    }\n                }\n            }\n        },\n        MuiButton: {\n            styleOverrides: {\n                root: {\n                    borderRadius: 12,\n                    textTransform: \"none\",\n                    fontWeight: 500,\n                    padding: \"8px 24px\",\n                    transition: \"all 0.2s ease-in-out\",\n                    \"&:hover\": {\n                        transform: \"translateY(-1px)\",\n                        boxShadow: \"0px 4px 12px rgba(0, 0, 0, 0.15)\"\n                    }\n                },\n                contained: {\n                    boxShadow: \"0px 2px 8px rgba(0, 0, 0, 0.15)\"\n                }\n            }\n        },\n        MuiChip: {\n            styleOverrides: {\n                root: {\n                    borderRadius: 8,\n                    fontWeight: 500\n                }\n            }\n        },\n        MuiPaper: {\n            styleOverrides: {\n                root: {\n                    borderRadius: 12\n                },\n                elevation1: {\n                    boxShadow: \"0px 2px 8px rgba(0, 0, 0, 0.1)\"\n                }\n            }\n        },\n        MuiAppBar: {\n            styleOverrides: {\n                root: {\n                    boxShadow: \"0px 2px 8px rgba(0, 0, 0, 0.1)\"\n                }\n            }\n        }\n    }\n});\nfunction MyApp(props) {\n    const { Component, emotionCache = clientSideEmotionCache, pageProps } = props;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Register service worker for PWA\n        if (\"serviceWorker\" in navigator) {\n            navigator.serviceWorker.register(\"/sw.js\").then((registration)=>{\n                console.log(\"SW registered: \", registration);\n            }).catch((registrationError)=>{\n                console.log(\"SW registration failed: \", registrationError);\n            });\n        }\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_emotion_react__WEBPACK_IMPORTED_MODULE_3__.CacheProvider, {\n        value: emotionCache,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"initial-scale=1, width=device-width\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_app.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Solana Trading Intelligence System\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_app.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_app.tsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__.QueryClientProvider, {\n                client: queryClient,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_styles__WEBPACK_IMPORTED_MODULE_4__.ThemeProvider, {\n                    theme: theme,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_5___default()), {}, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_app.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                            ...pageProps\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_app.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_7__.Toaster, {\n                            position: \"top-right\",\n                            toastOptions: {\n                                duration: 4000,\n                                style: {\n                                    background: \"#363636\",\n                                    color: \"#fff\",\n                                    borderRadius: \"12px\"\n                                },\n                                success: {\n                                    style: {\n                                        background: \"#2e7d32\"\n                                    }\n                                },\n                                error: {\n                                    style: {\n                                        background: \"#d32f2f\"\n                                    }\n                                }\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_app.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_app.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_app.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_app.tsx\",\n        lineNumber: 223,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./pages/_document.tsx":
/*!*****************************!*\
  !*** ./pages/_document.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MyDocument)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/document */ \"../../node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/createEmotionCache */ \"./utils/createEmotionCache.ts\");\n/* harmony import */ var _emotion_server_create_instance__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @emotion/server/create-instance */ \"@emotion/server/create-instance\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_3__, _emotion_server_create_instance__WEBPACK_IMPORTED_MODULE_4__]);\n([_utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_3__, _emotion_server_create_instance__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nclass MyDocument extends (next_document__WEBPACK_IMPORTED_MODULE_2___default()) {\n    static async getInitialProps(ctx) {\n        const originalRenderPage = ctx.renderPage;\n        const cache = (0,_utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_3__.createEmotionCache)();\n        const { extractCriticalToChunks } = (0,_emotion_server_create_instance__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(cache);\n        ctx.renderPage = ()=>originalRenderPage({\n                enhanceApp: (App)=>function EnhanceApp(props) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(App, {\n                            emotionCache: cache,\n                            ...props\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 20\n                        }, this);\n                    }\n            });\n        const initialProps = await next_document__WEBPACK_IMPORTED_MODULE_2___default().getInitialProps(ctx);\n        const emotionStyles = extractCriticalToChunks(initialProps.html);\n        const emotionStyleTags = emotionStyles.styles.map((style)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                \"data-emotion\": `${style.key} ${style.ids.join(\" \")}`,\n                dangerouslySetInnerHTML: {\n                    __html: style.css\n                }\n            }, style.key, false, {\n                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this));\n        return {\n            ...initialProps,\n            emotionStyleTags\n        };\n    }\n    render() {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.Html, {\n            lang: \"en\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.Head, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            charSet: \"utf-8\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"theme-color\",\n                            content: \"#1976d2\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"description\",\n                            content: \"Real-time Solana trading intelligence dashboard with whale tracking, rugpull detection, and advanced analytics\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"keywords\",\n                            content: \"solana, trading, intelligence, whale tracking, rugpull detection, crypto analytics, defi, blockchain\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"author\",\n                            content: \"HectorTa1989\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"robots\",\n                            content: \"index, follow\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            property: \"og:type\",\n                            content: \"website\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            property: \"og:title\",\n                            content: \"Solana Trading Intelligence System\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            property: \"og:description\",\n                            content: \"Real-time Solana trading intelligence dashboard with whale tracking, rugpull detection, and advanced analytics\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            property: \"og:image\",\n                            content: \"/og-image.png\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            property: \"og:url\",\n                            content: \"https://solana-intel.io\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            property: \"og:site_name\",\n                            content: \"Solana Trading Intelligence\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"twitter:card\",\n                            content: \"summary_large_image\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"twitter:title\",\n                            content: \"Solana Trading Intelligence System\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"twitter:description\",\n                            content: \"Real-time Solana trading intelligence dashboard with whale tracking, rugpull detection, and advanced analytics\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"twitter:image\",\n                            content: \"/twitter-image.png\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"twitter:creator\",\n                            content: \"@HectorTa1989\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            href: \"/favicon.ico\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            type: \"image/png\",\n                            sizes: \"32x32\",\n                            href: \"/favicon-32x32.png\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            type: \"image/png\",\n                            sizes: \"16x16\",\n                            href: \"/favicon-16x16.png\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"apple-touch-icon\",\n                            href: \"/apple-touch-icon.png\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"manifest\",\n                            href: \"/manifest.json\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"preconnect\",\n                            href: \"https://fonts.googleapis.com\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"preconnect\",\n                            href: \"https://fonts.gstatic.com\",\n                            crossOrigin: \"anonymous\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"stylesheet\",\n                            href: \"https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700&display=swap\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"stylesheet\",\n                            href: \"https://fonts.googleapis.com/icon?family=Material+Icons&display=swap\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                            type: \"application/ld+json\",\n                            dangerouslySetInnerHTML: {\n                                __html: JSON.stringify({\n                                    \"@context\": \"https://schema.org\",\n                                    \"@type\": \"WebApplication\",\n                                    \"name\": \"Solana Trading Intelligence System\",\n                                    \"description\": \"Real-time Solana trading intelligence dashboard with whale tracking, rugpull detection, and advanced analytics\",\n                                    \"url\": \"https://solana-intel.io\",\n                                    \"applicationCategory\": \"FinanceApplication\",\n                                    \"operatingSystem\": \"Web\",\n                                    \"author\": {\n                                        \"@type\": \"Person\",\n                                        \"name\": \"HectorTa1989\",\n                                        \"url\": \"https://github.com/HectorTa1989\"\n                                    },\n                                    \"offers\": {\n                                        \"@type\": \"Offer\",\n                                        \"price\": \"0\",\n                                        \"priceCurrency\": \"USD\"\n                                    }\n                                })\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"dns-prefetch\",\n                            href: \"//api.solana-intel.io\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"preconnect\",\n                            href: \"https://api.solana-intel.io\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this),\n                        this.props.emotionStyleTags\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.Main, {}, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.NextScript, {}, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n            lineNumber: 38,\n            columnNumber: 7\n        }, this);\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_document.tsx\n");

/***/ }),

/***/ "./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"../../node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,CardContent,CardHeader,Chip,Container,Grid,IconButton,Skeleton,Typography,useMediaQuery,useTheme!=!@mui/material */ \"__barrel_optimize__?names=Box,Card,CardContent,CardHeader,Chip,Container,Grid,IconButton,Skeleton,Typography,useMediaQuery,useTheme!=!../../node_modules/@mui/material/index.js\");\n/* harmony import */ var _mui_icons_material_TrendingUp__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/icons-material/TrendingUp */ \"@mui/icons-material/TrendingUp\");\n/* harmony import */ var _mui_icons_material_TrendingUp__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_mui_icons_material_TrendingUp__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _mui_icons_material_AccountBalanceWallet__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mui/icons-material/AccountBalanceWallet */ \"@mui/icons-material/AccountBalanceWallet\");\n/* harmony import */ var _mui_icons_material_AccountBalanceWallet__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_mui_icons_material_AccountBalanceWallet__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _mui_icons_material_ShowChart__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mui/icons-material/ShowChart */ \"@mui/icons-material/ShowChart\");\n/* harmony import */ var _mui_icons_material_ShowChart__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_mui_icons_material_ShowChart__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _mui_icons_material_Security__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @mui/icons-material/Security */ \"@mui/icons-material/Security\");\n/* harmony import */ var _mui_icons_material_Security__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_mui_icons_material_Security__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _mui_icons_material_Speed__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/icons-material/Speed */ \"@mui/icons-material/Speed\");\n/* harmony import */ var _mui_icons_material_Speed__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_mui_icons_material_Speed__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _mui_icons_material_Refresh__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mui/icons-material/Refresh */ \"@mui/icons-material/Refresh\");\n/* harmony import */ var _mui_icons_material_Refresh__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_mui_icons_material_Refresh__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../components/Layout */ \"./components/Layout.tsx\");\n/* harmony import */ var _components_MetricCard__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../components/MetricCard */ \"./components/MetricCard.tsx\");\n/* harmony import */ var _components_ClientOnly__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../components/ClientOnly */ \"./components/ClientOnly.tsx\");\n/* harmony import */ var _components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../components/ErrorBoundary */ \"./components/ErrorBoundary.tsx\");\n/* harmony import */ var _components_LoadingFallback__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../components/LoadingFallback */ \"./components/LoadingFallback.tsx\");\n/* harmony import */ var _utils_formatters__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../utils/formatters */ \"./utils/formatters.ts\");\n/* harmony import */ var _hooks_useRealTimeData__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../hooks/useRealTimeData */ \"./hooks/useRealTimeData.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_hooks_useRealTimeData__WEBPACK_IMPORTED_MODULE_17__]);\n_hooks_useRealTimeData__WEBPACK_IMPORTED_MODULE_17__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Dynamic imports for components that use WebSocket/real-time data\nconst TradingChart = next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/recharts\"), __webpack_require__.e(\"components_TradingChart_tsx\")]).then(__webpack_require__.bind(__webpack_require__, /*! ../components/TradingChart */ \"./components/TradingChart.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"index.tsx -> \" + \"../components/TradingChart\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Skeleton, {\n            variant: \"rectangular\",\n            height: 400\n        }, void 0, false, {\n            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n            lineNumber: 40,\n            columnNumber: 18\n        }, undefined)\n});\nconst WalletLeaderboard = next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/@mui\"), __webpack_require__.e(\"components_WalletLeaderboard_tsx\")]).then(__webpack_require__.bind(__webpack_require__, /*! ../components/WalletLeaderboard */ \"./components/WalletLeaderboard.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"index.tsx -> \" + \"../components/WalletLeaderboard\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Skeleton, {\n            variant: \"rectangular\",\n            height: 300\n        }, void 0, false, {\n            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n            lineNumber: 45,\n            columnNumber: 18\n        }, undefined)\n});\nconst AlertsPanel = next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/@mui\"), __webpack_require__.e(\"components_AlertsPanel_tsx\")]).then(__webpack_require__.bind(__webpack_require__, /*! ../components/AlertsPanel */ \"./components/AlertsPanel.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"index.tsx -> \" + \"../components/AlertsPanel\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Skeleton, {\n            variant: \"rectangular\",\n            height: 300\n        }, void 0, false, {\n            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n            lineNumber: 50,\n            columnNumber: 18\n        }, undefined)\n});\nconst VolumeHeatmap = next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/@mui\"), __webpack_require__.e(\"components_VolumeHeatmap_tsx\")]).then(__webpack_require__.bind(__webpack_require__, /*! ../components/VolumeHeatmap */ \"./components/VolumeHeatmap.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"index.tsx -> \" + \"../components/VolumeHeatmap\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Skeleton, {\n            variant: \"rectangular\",\n            height: 300\n        }, void 0, false, {\n            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n            lineNumber: 55,\n            columnNumber: 18\n        }, undefined)\n});\nconst RecentTrades = next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/@mui\"), __webpack_require__.e(\"components_RecentTrades_tsx\")]).then(__webpack_require__.bind(__webpack_require__, /*! ../components/RecentTrades */ \"./components/RecentTrades.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"index.tsx -> \" + \"../components/RecentTrades\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Skeleton, {\n            variant: \"rectangular\",\n            height: 400\n        }, void 0, false, {\n            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n            lineNumber: 60,\n            columnNumber: 18\n        }, undefined)\n});\n// Import the real hook\n\nfunction Dashboard() {\n    const theme = (0,_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.useTheme)();\n    const isMobile = (0,_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.useMediaQuery)(theme.breakpoints.down(\"md\"));\n    const { metrics, trades, alerts, isConnected, loading, error } = (0,_hooks_useRealTimeData__WEBPACK_IMPORTED_MODULE_17__.useRealTimeData)();\n    const [lastUpdate, setLastUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n        setLastUpdate(new Date());\n        const interval = setInterval(()=>{\n            setLastUpdate(new Date());\n        }, 30000); // Update every 30 seconds\n        return ()=>clearInterval(interval);\n    }, []);\n    const handleRefresh = ()=>{\n        setLastUpdate(new Date());\n        // Trigger data refresh by reloading the page or calling API\n        window.location.reload();\n    };\n    // Show loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingFallback__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n            type: \"page\",\n            message: \"Loading trading intelligence...\"\n        }, void 0, false, {\n            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n            lineNumber: 93,\n            columnNumber: 12\n        }, this);\n    }\n    // Show error state\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n            fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingFallback__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                type: \"page\",\n                message: `Error: ${error}. Please try refreshing the page.`\n            }, void 0, false, void 0, void 0)\n        }, void 0, false, {\n            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n            lineNumber: 99,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            title: \"Dashboard - Solana Trading Intelligence\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: \"Dashboard - Solana Trading Intelligence\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"description\",\n                            content: \"Real-time Solana trading intelligence dashboard\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Container, {\n                    maxWidth: \"xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Box, {\n                            display: \"flex\",\n                            justifyContent: \"space-between\",\n                            alignItems: \"center\",\n                            mb: 3,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Box, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                            variant: \"h4\",\n                                            component: \"h1\",\n                                            fontWeight: \"bold\",\n                                            gutterBottom: true,\n                                            children: \"Trading Intelligence Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Box, {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: 2,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Chip, {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_icons_material_Speed__WEBPACK_IMPORTED_MODULE_8___default()), {}, void 0, false, void 0, void 0),\n                                                    label: isConnected ? \"LIVE\" : \"DISCONNECTED\",\n                                                    color: isConnected ? \"success\" : \"error\",\n                                                    size: \"small\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                                    variant: \"body2\",\n                                                    color: \"text.secondary\",\n                                                    suppressHydrationWarning: true,\n                                                    children: [\n                                                        \"Last updated: \",\n                                                        mounted && lastUpdate ? lastUpdate.toLocaleTimeString() : \"--:--:--\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.IconButton, {\n                                    onClick: handleRefresh,\n                                    color: \"primary\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_icons_material_Refresh__WEBPACK_IMPORTED_MODULE_9___default()), {}, void 0, false, {\n                                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Grid, {\n                            container: true,\n                            spacing: 3,\n                            mb: 4,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Grid, {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MetricCard__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        title: \"24h Volume\",\n                                        value: (0,_utils_formatters__WEBPACK_IMPORTED_MODULE_15__.formatCurrency)(metrics.totalVolume24h),\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_icons_material_ShowChart__WEBPACK_IMPORTED_MODULE_6___default()), {}, void 0, false, void 0, void 0),\n                                        trend: 12.5,\n                                        color: \"primary\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Grid, {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MetricCard__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        title: \"24h Trades\",\n                                        value: (0,_utils_formatters__WEBPACK_IMPORTED_MODULE_15__.formatNumber)(metrics.totalTrades24h),\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_icons_material_TrendingUp__WEBPACK_IMPORTED_MODULE_4___default()), {}, void 0, false, void 0, void 0),\n                                        trend: 8.3,\n                                        color: \"success\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Grid, {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MetricCard__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        title: \"Active Wallets\",\n                                        value: (0,_utils_formatters__WEBPACK_IMPORTED_MODULE_15__.formatNumber)(metrics.activeWallets24h),\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_icons_material_AccountBalanceWallet__WEBPACK_IMPORTED_MODULE_5___default()), {}, void 0, false, void 0, void 0),\n                                        trend: -2.1,\n                                        color: \"info\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Grid, {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MetricCard__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        title: \"Avg PnL\",\n                                        value: (0,_utils_formatters__WEBPACK_IMPORTED_MODULE_15__.formatCurrency)(metrics.avgPnL24h),\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_icons_material_Security__WEBPACK_IMPORTED_MODULE_7___default()), {}, void 0, false, void 0, void 0),\n                                        trend: 15.7,\n                                        color: \"warning\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Grid, {\n                            container: true,\n                            spacing: 3,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Grid, {\n                                    item: true,\n                                    xs: 12,\n                                    lg: 8,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardHeader, {\n                                                title: \"Trading Volume & Price\",\n                                                subheader: \"Real-time trading activity across all DEXs\",\n                                                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Chip, {\n                                                    label: \"24H\",\n                                                    size: \"small\",\n                                                    variant: \"outlined\"\n                                                }, void 0, false, void 0, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Skeleton, {\n                                                        variant: \"rectangular\",\n                                                        height: 400\n                                                    }, void 0, false, void 0, void 0),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TradingChart, {\n                                                        data: []\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Grid, {\n                                    item: true,\n                                    xs: 12,\n                                    lg: 4,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardHeader, {\n                                                title: \"Top Performers\",\n                                                subheader: \"Highest PnL wallets\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Skeleton, {\n                                                        variant: \"rectangular\",\n                                                        height: 300\n                                                    }, void 0, false, void 0, void 0),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WalletLeaderboard, {}, void 0, false, {\n                                                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Grid, {\n                                    item: true,\n                                    xs: 12,\n                                    lg: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardHeader, {\n                                                title: \"Token Volume Heatmap\",\n                                                subheader: \"Volume and price changes by token\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Skeleton, {\n                                                        variant: \"rectangular\",\n                                                        height: 300\n                                                    }, void 0, false, void 0, void 0),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VolumeHeatmap, {}, void 0, false, {\n                                                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Grid, {\n                                    item: true,\n                                    xs: 12,\n                                    lg: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardHeader, {\n                                                title: \"Real-time Alerts\",\n                                                subheader: \"Suspicious activities and notifications\",\n                                                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Chip, {\n                                                    label: `${alerts.length} Active`,\n                                                    color: \"warning\",\n                                                    size: \"small\"\n                                                }, void 0, false, void 0, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Skeleton, {\n                                                        variant: \"rectangular\",\n                                                        height: 300\n                                                    }, void 0, false, void 0, void 0),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AlertsPanel, {\n                                                        alerts: alerts\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Grid, {\n                                    item: true,\n                                    xs: 12,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardHeader, {\n                                                title: \"Recent Trades\",\n                                                subheader: \"Latest trading activity across all DEXs\",\n                                                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Box, {\n                                                    display: \"flex\",\n                                                    gap: 1,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Chip, {\n                                                            label: \"Real-time\",\n                                                            color: \"success\",\n                                                            size: \"small\"\n                                                        }, void 0, false, void 0, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Chip, {\n                                                            label: `${trades.length} Trades`,\n                                                            size: \"small\",\n                                                            variant: \"outlined\"\n                                                        }, void 0, false, void 0, void 0)\n                                                    ]\n                                                }, void 0, true, void 0, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_CardHeader_Chip_Container_Grid_IconButton_Skeleton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Skeleton, {\n                                                        variant: \"rectangular\",\n                                                        height: 400\n                                                    }, void 0, false, void 0, void 0),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RecentTrades, {\n                                                        trades: trades\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n            lineNumber: 112,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\index.tsx\",\n        lineNumber: 111,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/index.tsx\n");

/***/ }),

/***/ "./services/api.ts":
/*!*************************!*\
  !*** ./services/api.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiService: () => (/* binding */ apiService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"axios\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([axios__WEBPACK_IMPORTED_MODULE_0__, react_hot_toast__WEBPACK_IMPORTED_MODULE_1__]);\n([axios__WEBPACK_IMPORTED_MODULE_0__, react_hot_toast__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n// API Configuration\nconst API_BASE_URL = \"http://localhost:3005\" || 0;\nconst API_TIMEOUT = 30000; // 30 seconds\nconst RETRY_ATTEMPTS = 3;\nconst RETRY_DELAY = 1000; // 1 second\n// Rate Limiting\nclass RateLimiter {\n    canMakeRequest(endpoint) {\n        const now = Date.now();\n        const requests = this.requests.get(endpoint) || [];\n        // Remove old requests outside the window\n        const validRequests = requests.filter((time)=>now - time < this.windowMs);\n        if (validRequests.length >= this.maxRequests) {\n            return false;\n        }\n        validRequests.push(now);\n        this.requests.set(endpoint, validRequests);\n        return true;\n    }\n    getRemainingRequests(endpoint) {\n        const now = Date.now();\n        const requests = this.requests.get(endpoint) || [];\n        const validRequests = requests.filter((time)=>now - time < this.windowMs);\n        return Math.max(0, this.maxRequests - validRequests.length);\n    }\n    constructor(){\n        this.requests = new Map();\n        this.maxRequests = 100;\n        this.windowMs = 15 * 60 * 1000 // 15 minutes\n        ;\n    }\n}\nconst rateLimiter = new RateLimiter();\n// Retry Logic\nconst sleep = (ms)=>new Promise((resolve)=>setTimeout(resolve, ms));\nasync function retryRequest(requestFn, attempts = RETRY_ATTEMPTS, delay = RETRY_DELAY) {\n    try {\n        return await requestFn();\n    } catch (error) {\n        if (attempts <= 1) {\n            throw error;\n        }\n        // Only retry on network errors or 5xx status codes\n        const shouldRetry = !error.response || error.response.status >= 500 && error.response.status < 600 || error.code === \"NETWORK_ERROR\" || error.code === \"TIMEOUT\";\n        if (!shouldRetry) {\n            throw error;\n        }\n        await sleep(delay);\n        return retryRequest(requestFn, attempts - 1, delay * 2);\n    }\n}\n// Create Axios Instance\nconst createApiInstance = ()=>{\n    const instance = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n        baseURL: API_BASE_URL,\n        timeout: API_TIMEOUT,\n        headers: {\n            \"Content-Type\": \"application/json\",\n            \"X-API-Key\": process.env.NEXT_PUBLIC_API_KEY || \"dev-key-123\"\n        }\n    });\n    // Request Interceptor\n    instance.interceptors.request.use((config)=>{\n        // Rate limiting check\n        const endpoint = config.url || \"\";\n        if (!rateLimiter.canMakeRequest(endpoint)) {\n            const remaining = rateLimiter.getRemainingRequests(endpoint);\n            throw new Error(`Rate limit exceeded. ${remaining} requests remaining.`);\n        }\n        // Add timestamp for request tracking\n        config.metadata = {\n            startTime: Date.now()\n        };\n        // Add auth token if available\n        if (false) {}\n        // Add request ID for tracking\n        config.headers[\"X-Request-ID\"] = Math.random().toString(36).substring(2, 11);\n        return config;\n    }, (error)=>{\n        console.error(\"Request interceptor error:\", error);\n        return Promise.reject(error);\n    });\n    // Response Interceptor\n    instance.interceptors.response.use((response)=>{\n        // Log response time in development\n        if (true) {\n            const duration = Date.now() - (response.config.metadata?.startTime || 0);\n            console.log(`API ${response.config.method?.toUpperCase()} ${response.config.url}: ${duration}ms`);\n        }\n        return response.data;\n    }, (error)=>{\n        // Enhanced error handling\n        const errorResponse = {\n            success: false,\n            error: \"Unknown Error\",\n            message: \"An unexpected error occurred\",\n            statusCode: 500,\n            timestamp: new Date().toISOString()\n        };\n        if (error.response) {\n            // Server responded with error status\n            errorResponse.statusCode = error.response.status;\n            errorResponse.error = error.response.data?.error || \"Server Error\";\n            errorResponse.message = error.response.data?.message || error.message;\n            // Handle specific status codes\n            switch(error.response.status){\n                case 401:\n                    errorResponse.message = \"Authentication required\";\n                    if (false) {}\n                    break;\n                case 403:\n                    errorResponse.message = \"Access forbidden\";\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_1__[\"default\"].error(\"You do not have permission to perform this action.\");\n                    break;\n                case 404:\n                    errorResponse.message = \"Resource not found\";\n                    break;\n                case 429:\n                    errorResponse.message = \"Too many requests. Please try again later.\";\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_1__[\"default\"].error(\"Rate limit exceeded. Please slow down.\");\n                    break;\n                case 500:\n                    errorResponse.message = \"Internal server error\";\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_1__[\"default\"].error(\"Server error. Please try again later.\");\n                    break;\n                default:\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_1__[\"default\"].error(errorResponse.message);\n            }\n        } else if (error.request) {\n            // Network error\n            errorResponse.error = \"Network Error\";\n            errorResponse.message = \"Unable to connect to server. Please check your internet connection.\";\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_1__[\"default\"].error(\"Network error. Please check your connection.\");\n        } else {\n            // Request setup error\n            errorResponse.error = \"Request Error\";\n            errorResponse.message = error.message;\n        }\n        console.error(\"API Error:\", errorResponse);\n        return Promise.reject(errorResponse);\n    });\n    return instance;\n};\nconst api = createApiInstance();\n// API Service Methods\nconst apiService = {\n    // Metrics endpoints\n    metrics: {\n        getRealTime: ()=>retryRequest(()=>api.get(\"/api/metrics\")),\n        getHistorical: (timeframe, interval)=>retryRequest(()=>api.get(`/api/metrics/historical?timeframe=${timeframe}&interval=${interval}`)),\n        getVolume: (dex, token)=>retryRequest(()=>api.get(`/api/metrics/volume?dex=${dex || \"\"}&token=${token || \"\"}`)),\n        getPnL: (wallet, timeframe)=>retryRequest(()=>api.get(`/api/metrics/pnl?wallet=${wallet}&timeframe=${timeframe}`)),\n        getDexComparison: (timeframe)=>retryRequest(()=>api.get(`/api/metrics/dex-comparison?timeframe=${timeframe}`)),\n        getTopTokens: (limit, timeframe)=>retryRequest(()=>api.get(`/api/metrics/top-tokens?limit=${limit}&timeframe=${timeframe}`))\n    },\n    // Trades endpoints\n    trades: {\n        getRecent: (params)=>retryRequest(()=>api.get(\"/api/trades\", {\n                    params\n                })),\n        getBySignature: (signature)=>retryRequest(()=>api.get(`/api/trades/${signature}`)),\n        getByWallet: (address, params)=>retryRequest(()=>api.get(`/api/trades/wallet/${address}`, {\n                    params\n                })),\n        getWhales: (params)=>retryRequest(()=>api.get(\"/api/trades/whales\", {\n                    params\n                })),\n        getSuspicious: (params)=>retryRequest(()=>api.get(\"/api/trades/suspicious\", {\n                    params\n                })),\n        getAnalytics: (timeframe)=>retryRequest(()=>api.get(`/api/trades/analytics/summary?timeframe=${timeframe}`))\n    },\n    // Alerts endpoints\n    alerts: {\n        getActive: (params)=>retryRequest(()=>api.get(\"/api/alerts\", {\n                    params\n                })),\n        getById: (id)=>retryRequest(()=>api.get(`/api/alerts/${id}`)),\n        resolve: (id)=>retryRequest(()=>api.patch(`/api/alerts/${id}/resolve`))\n    },\n    // Wallets endpoints\n    wallets: {\n        getLeaderboard: (params)=>retryRequest(()=>api.get(\"/api/wallets/leaderboard\", {\n                    params\n                })),\n        getDetails: (address, timeframe)=>retryRequest(()=>api.get(`/api/wallets/${address}?timeframe=${timeframe}`)),\n        getTrades: (address, params)=>retryRequest(()=>api.get(`/api/wallets/${address}/trades`, {\n                    params\n                })),\n        getPnL: (address, timeframe, interval)=>retryRequest(()=>api.get(`/api/wallets/${address}/pnl?timeframe=${timeframe}&interval=${interval}`))\n    },\n    // Analytics endpoints\n    analytics: {\n        getMarket: (timeframe)=>retryRequest(()=>api.get(`/api/analytics/market?timeframe=${timeframe}`)),\n        getToken: (symbol, timeframe)=>retryRequest(()=>api.get(`/api/analytics/tokens/${symbol}?timeframe=${timeframe}`)),\n        getDex: (name, timeframe)=>retryRequest(()=>api.get(`/api/analytics/dex/${name}?timeframe=${timeframe}`)),\n        getWhales: (timeframe, minAmount)=>retryRequest(()=>api.get(`/api/analytics/whales?timeframe=${timeframe}&minAmount=${minAmount}`))\n    },\n    // Health check\n    health: ()=>retryRequest(()=>api.get(\"/health\"))\n};\n// Export default api instance\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (api);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./services/api.ts\n");

/***/ }),

/***/ "./utils/createEmotionCache.ts":
/*!*************************************!*\
  !*** ./utils/createEmotionCache.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createEmotionCache: () => (/* binding */ createEmotionCache),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _emotion_cache__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @emotion/cache */ \"@emotion/cache\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_emotion_cache__WEBPACK_IMPORTED_MODULE_0__]);\n_emotion_cache__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nconst isBrowser = typeof document !== \"undefined\";\n// On the client side, Create a meta tag at the top of the <head> and set it as insertionPoint.\n// This assures that MUI styles are loaded first.\n// It allows developers to easily override MUI styles with other styling solutions, like CSS modules.\nfunction createEmotionCache() {\n    let insertionPoint;\n    if (isBrowser) {\n        const emotionInsertionPoint = document.querySelector('meta[name=\"emotion-insertion-point\"]');\n        insertionPoint = emotionInsertionPoint ?? undefined;\n    }\n    return (0,_emotion_cache__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n        key: \"mui-style\",\n        insertionPoint\n    });\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (createEmotionCache);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi91dGlscy9jcmVhdGVFbW90aW9uQ2FjaGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQXlDO0FBRXpDLE1BQU1DLFlBQVksT0FBT0MsYUFBYTtBQUV0QywrRkFBK0Y7QUFDL0YsaURBQWlEO0FBQ2pELHFHQUFxRztBQUNyRyxTQUFTQztJQUNQLElBQUlDO0lBRUosSUFBSUgsV0FBVztRQUNiLE1BQU1JLHdCQUF3QkgsU0FBU0ksYUFBYSxDQUNsRDtRQUVGRixpQkFBaUJDLHlCQUF5QkU7SUFDNUM7SUFFQSxPQUFPUCwwREFBV0EsQ0FBQztRQUFFUSxLQUFLO1FBQWFKO0lBQWU7QUFDeEQ7QUFFOEI7QUFDOUIsaUVBQWVELGtCQUFrQkEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL3NvbGFuYS10cmFkaW5nLWRhc2hib2FyZC8uL3V0aWxzL2NyZWF0ZUVtb3Rpb25DYWNoZS50cz8xY2U2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVDYWNoZSBmcm9tICdAZW1vdGlvbi9jYWNoZSc7XG5cbmNvbnN0IGlzQnJvd3NlciA9IHR5cGVvZiBkb2N1bWVudCAhPT0gJ3VuZGVmaW5lZCc7XG5cbi8vIE9uIHRoZSBjbGllbnQgc2lkZSwgQ3JlYXRlIGEgbWV0YSB0YWcgYXQgdGhlIHRvcCBvZiB0aGUgPGhlYWQ+IGFuZCBzZXQgaXQgYXMgaW5zZXJ0aW9uUG9pbnQuXG4vLyBUaGlzIGFzc3VyZXMgdGhhdCBNVUkgc3R5bGVzIGFyZSBsb2FkZWQgZmlyc3QuXG4vLyBJdCBhbGxvd3MgZGV2ZWxvcGVycyB0byBlYXNpbHkgb3ZlcnJpZGUgTVVJIHN0eWxlcyB3aXRoIG90aGVyIHN0eWxpbmcgc29sdXRpb25zLCBsaWtlIENTUyBtb2R1bGVzLlxuZnVuY3Rpb24gY3JlYXRlRW1vdGlvbkNhY2hlKCkge1xuICBsZXQgaW5zZXJ0aW9uUG9pbnQ7XG5cbiAgaWYgKGlzQnJvd3Nlcikge1xuICAgIGNvbnN0IGVtb3Rpb25JbnNlcnRpb25Qb2ludCA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3I8SFRNTE1ldGFFbGVtZW50PihcbiAgICAgICdtZXRhW25hbWU9XCJlbW90aW9uLWluc2VydGlvbi1wb2ludFwiXScsXG4gICAgKTtcbiAgICBpbnNlcnRpb25Qb2ludCA9IGVtb3Rpb25JbnNlcnRpb25Qb2ludCA/PyB1bmRlZmluZWQ7XG4gIH1cblxuICByZXR1cm4gY3JlYXRlQ2FjaGUoeyBrZXk6ICdtdWktc3R5bGUnLCBpbnNlcnRpb25Qb2ludCB9KTtcbn1cblxuZXhwb3J0IHsgY3JlYXRlRW1vdGlvbkNhY2hlIH07XG5leHBvcnQgZGVmYXVsdCBjcmVhdGVFbW90aW9uQ2FjaGU7XG4iXSwibmFtZXMiOlsiY3JlYXRlQ2FjaGUiLCJpc0Jyb3dzZXIiLCJkb2N1bWVudCIsImNyZWF0ZUVtb3Rpb25DYWNoZSIsImluc2VydGlvblBvaW50IiwiZW1vdGlvbkluc2VydGlvblBvaW50IiwicXVlcnlTZWxlY3RvciIsInVuZGVmaW5lZCIsImtleSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./utils/createEmotionCache.ts\n");

/***/ }),

/***/ "./utils/formatters.ts":
/*!*****************************!*\
  !*** ./utils/formatters.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatAddress: () => (/* binding */ formatAddress),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatPercentage: () => (/* binding */ formatPercentage),\n/* harmony export */   formatTimeAgo: () => (/* binding */ formatTimeAgo)\n/* harmony export */ });\nconst formatNumber = (num)=>{\n    if (num >= 1000000000) {\n        return `${(num / 1000000000).toFixed(1)}B`;\n    }\n    if (num >= 1000000) {\n        return `${(num / 1000000).toFixed(1)}M`;\n    }\n    if (num >= 1000) {\n        return `${(num / 1000).toFixed(1)}K`;\n    }\n    return num.toFixed(0);\n};\nconst formatCurrency = (value)=>{\n    return new Intl.NumberFormat(\"en-US\", {\n        style: \"currency\",\n        currency: \"USD\",\n        minimumFractionDigits: 0,\n        maximumFractionDigits: 2\n    }).format(value);\n};\nconst formatPercentage = (value)=>{\n    return `${(value * 100).toFixed(1)}%`;\n};\nconst formatAddress = (address)=>{\n    return `${address.slice(0, 4)}...${address.slice(-4)}`;\n};\nconst formatTimeAgo = (timestamp)=>{\n    const now = new Date();\n    const time = new Date(timestamp);\n    const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return \"Just now\";\n    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h ago`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d ago`;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/formatters.ts\n");

/***/ }),

/***/ "@mui/icons-material/AccountBalanceWallet":
/*!***********************************************************!*\
  !*** external "@mui/icons-material/AccountBalanceWallet" ***!
  \***********************************************************/
/***/ ((module) => {

module.exports = require("@mui/icons-material/AccountBalanceWallet");

/***/ }),

/***/ "@mui/icons-material/AccountCircle":
/*!****************************************************!*\
  !*** external "@mui/icons-material/AccountCircle" ***!
  \****************************************************/
/***/ ((module) => {

module.exports = require("@mui/icons-material/AccountCircle");

/***/ }),

/***/ "@mui/icons-material/CheckCircle":
/*!**************************************************!*\
  !*** external "@mui/icons-material/CheckCircle" ***!
  \**************************************************/
/***/ ((module) => {

module.exports = require("@mui/icons-material/CheckCircle");

/***/ }),

/***/ "@mui/icons-material/Error":
/*!********************************************!*\
  !*** external "@mui/icons-material/Error" ***!
  \********************************************/
/***/ ((module) => {

module.exports = require("@mui/icons-material/Error");

/***/ }),

/***/ "@mui/icons-material/ErrorOutline":
/*!***************************************************!*\
  !*** external "@mui/icons-material/ErrorOutline" ***!
  \***************************************************/
/***/ ((module) => {

module.exports = require("@mui/icons-material/ErrorOutline");

/***/ }),

/***/ "@mui/icons-material/Home":
/*!*******************************************!*\
  !*** external "@mui/icons-material/Home" ***!
  \*******************************************/
/***/ ((module) => {

module.exports = require("@mui/icons-material/Home");

/***/ }),

/***/ "@mui/icons-material/Info":
/*!*******************************************!*\
  !*** external "@mui/icons-material/Info" ***!
  \*******************************************/
/***/ ((module) => {

module.exports = require("@mui/icons-material/Info");

/***/ }),

/***/ "@mui/icons-material/Menu":
/*!*******************************************!*\
  !*** external "@mui/icons-material/Menu" ***!
  \*******************************************/
/***/ ((module) => {

module.exports = require("@mui/icons-material/Menu");

/***/ }),

/***/ "@mui/icons-material/Notifications":
/*!****************************************************!*\
  !*** external "@mui/icons-material/Notifications" ***!
  \****************************************************/
/***/ ((module) => {

module.exports = require("@mui/icons-material/Notifications");

/***/ }),

/***/ "@mui/icons-material/Refresh":
/*!**********************************************!*\
  !*** external "@mui/icons-material/Refresh" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("@mui/icons-material/Refresh");

/***/ }),

/***/ "@mui/icons-material/Security":
/*!***********************************************!*\
  !*** external "@mui/icons-material/Security" ***!
  \***********************************************/
/***/ ((module) => {

module.exports = require("@mui/icons-material/Security");

/***/ }),

/***/ "@mui/icons-material/ShowChart":
/*!************************************************!*\
  !*** external "@mui/icons-material/ShowChart" ***!
  \************************************************/
/***/ ((module) => {

module.exports = require("@mui/icons-material/ShowChart");

/***/ }),

/***/ "@mui/icons-material/Speed":
/*!********************************************!*\
  !*** external "@mui/icons-material/Speed" ***!
  \********************************************/
/***/ ((module) => {

module.exports = require("@mui/icons-material/Speed");

/***/ }),

/***/ "@mui/icons-material/SwapHoriz":
/*!************************************************!*\
  !*** external "@mui/icons-material/SwapHoriz" ***!
  \************************************************/
/***/ ((module) => {

module.exports = require("@mui/icons-material/SwapHoriz");

/***/ }),

/***/ "@mui/icons-material/TrendingDown":
/*!***************************************************!*\
  !*** external "@mui/icons-material/TrendingDown" ***!
  \***************************************************/
/***/ ((module) => {

module.exports = require("@mui/icons-material/TrendingDown");

/***/ }),

/***/ "@mui/icons-material/TrendingUp":
/*!*************************************************!*\
  !*** external "@mui/icons-material/TrendingUp" ***!
  \*************************************************/
/***/ ((module) => {

module.exports = require("@mui/icons-material/TrendingUp");

/***/ }),

/***/ "@mui/icons-material/Warning":
/*!**********************************************!*\
  !*** external "@mui/icons-material/Warning" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("@mui/icons-material/Warning");

/***/ }),

/***/ "@mui/material/CssBaseline":
/*!********************************************!*\
  !*** external "@mui/material/CssBaseline" ***!
  \********************************************/
/***/ ((module) => {

module.exports = require("@mui/material/CssBaseline");

/***/ }),

/***/ "@mui/material/styles":
/*!***************************************!*\
  !*** external "@mui/material/styles" ***!
  \***************************************/
/***/ ((module) => {

module.exports = require("@mui/material/styles");

/***/ }),

/***/ "@mui/system":
/*!******************************!*\
  !*** external "@mui/system" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("@mui/system");

/***/ }),

/***/ "@mui/system/DefaultPropsProvider":
/*!***************************************************!*\
  !*** external "@mui/system/DefaultPropsProvider" ***!
  \***************************************************/
/***/ ((module) => {

module.exports = require("@mui/system/DefaultPropsProvider");

/***/ }),

/***/ "@mui/system/InitColorSchemeScript":
/*!****************************************************!*\
  !*** external "@mui/system/InitColorSchemeScript" ***!
  \****************************************************/
/***/ ((module) => {

module.exports = require("@mui/system/InitColorSchemeScript");

/***/ }),

/***/ "@mui/system/RtlProvider":
/*!******************************************!*\
  !*** external "@mui/system/RtlProvider" ***!
  \******************************************/
/***/ ((module) => {

module.exports = require("@mui/system/RtlProvider");

/***/ }),

/***/ "@mui/system/colorManipulator":
/*!***********************************************!*\
  !*** external "@mui/system/colorManipulator" ***!
  \***********************************************/
/***/ ((module) => {

module.exports = require("@mui/system/colorManipulator");

/***/ }),

/***/ "@mui/system/createStyled":
/*!*******************************************!*\
  !*** external "@mui/system/createStyled" ***!
  \*******************************************/
/***/ ((module) => {

module.exports = require("@mui/system/createStyled");

/***/ }),

/***/ "@mui/system/createTheme":
/*!******************************************!*\
  !*** external "@mui/system/createTheme" ***!
  \******************************************/
/***/ ((module) => {

module.exports = require("@mui/system/createTheme");

/***/ }),

/***/ "@mui/system/styleFunctionSx":
/*!**********************************************!*\
  !*** external "@mui/system/styleFunctionSx" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("@mui/system/styleFunctionSx");

/***/ }),

/***/ "@mui/system/useMediaQuery":
/*!********************************************!*\
  !*** external "@mui/system/useMediaQuery" ***!
  \********************************************/
/***/ ((module) => {

module.exports = require("@mui/system/useMediaQuery");

/***/ }),

/***/ "@mui/system/useThemeProps":
/*!********************************************!*\
  !*** external "@mui/system/useThemeProps" ***!
  \********************************************/
/***/ ((module) => {

module.exports = require("@mui/system/useThemeProps");

/***/ }),

/***/ "@mui/system/useThemeWithoutDefault":
/*!*****************************************************!*\
  !*** external "@mui/system/useThemeWithoutDefault" ***!
  \*****************************************************/
/***/ ((module) => {

module.exports = require("@mui/system/useThemeWithoutDefault");

/***/ }),

/***/ "@mui/utils":
/*!*****************************!*\
  !*** external "@mui/utils" ***!
  \*****************************/
/***/ ((module) => {

module.exports = require("@mui/utils");

/***/ }),

/***/ "@mui/utils/HTMLElementType":
/*!*********************************************!*\
  !*** external "@mui/utils/HTMLElementType" ***!
  \*********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/HTMLElementType");

/***/ }),

/***/ "@mui/utils/appendOwnerState":
/*!**********************************************!*\
  !*** external "@mui/utils/appendOwnerState" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/appendOwnerState");

/***/ }),

/***/ "@mui/utils/capitalize":
/*!****************************************!*\
  !*** external "@mui/utils/capitalize" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/capitalize");

/***/ }),

/***/ "@mui/utils/chainPropTypes":
/*!********************************************!*\
  !*** external "@mui/utils/chainPropTypes" ***!
  \********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/chainPropTypes");

/***/ }),

/***/ "@mui/utils/composeClasses":
/*!********************************************!*\
  !*** external "@mui/utils/composeClasses" ***!
  \********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/composeClasses");

/***/ }),

/***/ "@mui/utils/deepmerge":
/*!***************************************!*\
  !*** external "@mui/utils/deepmerge" ***!
  \***************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/deepmerge");

/***/ }),

/***/ "@mui/utils/elementAcceptingRef":
/*!*************************************************!*\
  !*** external "@mui/utils/elementAcceptingRef" ***!
  \*************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/elementAcceptingRef");

/***/ }),

/***/ "@mui/utils/elementTypeAcceptingRef":
/*!*****************************************************!*\
  !*** external "@mui/utils/elementTypeAcceptingRef" ***!
  \*****************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/elementTypeAcceptingRef");

/***/ }),

/***/ "@mui/utils/formatMuiErrorMessage":
/*!***************************************************!*\
  !*** external "@mui/utils/formatMuiErrorMessage" ***!
  \***************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/formatMuiErrorMessage");

/***/ }),

/***/ "@mui/utils/generateUtilityClass":
/*!**************************************************!*\
  !*** external "@mui/utils/generateUtilityClass" ***!
  \**************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/generateUtilityClass");

/***/ }),

/***/ "@mui/utils/generateUtilityClasses":
/*!****************************************************!*\
  !*** external "@mui/utils/generateUtilityClasses" ***!
  \****************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/generateUtilityClasses");

/***/ }),

/***/ "@mui/utils/getReactElementRef":
/*!************************************************!*\
  !*** external "@mui/utils/getReactElementRef" ***!
  \************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/getReactElementRef");

/***/ }),

/***/ "@mui/utils/integerPropType":
/*!*********************************************!*\
  !*** external "@mui/utils/integerPropType" ***!
  \*********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/integerPropType");

/***/ }),

/***/ "@mui/utils/isHostComponent":
/*!*********************************************!*\
  !*** external "@mui/utils/isHostComponent" ***!
  \*********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/isHostComponent");

/***/ }),

/***/ "@mui/utils/isMuiElement":
/*!******************************************!*\
  !*** external "@mui/utils/isMuiElement" ***!
  \******************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/isMuiElement");

/***/ }),

/***/ "@mui/utils/mergeSlotProps":
/*!********************************************!*\
  !*** external "@mui/utils/mergeSlotProps" ***!
  \********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/mergeSlotProps");

/***/ }),

/***/ "@mui/utils/refType":
/*!*************************************!*\
  !*** external "@mui/utils/refType" ***!
  \*************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/refType");

/***/ }),

/***/ "@mui/utils/requirePropFactory":
/*!************************************************!*\
  !*** external "@mui/utils/requirePropFactory" ***!
  \************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/requirePropFactory");

/***/ }),

/***/ "@mui/utils/resolveComponentProps":
/*!***************************************************!*\
  !*** external "@mui/utils/resolveComponentProps" ***!
  \***************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/resolveComponentProps");

/***/ }),

/***/ "@mui/utils/resolveProps":
/*!******************************************!*\
  !*** external "@mui/utils/resolveProps" ***!
  \******************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/resolveProps");

/***/ }),

/***/ "@mui/utils/unsupportedProp":
/*!*********************************************!*\
  !*** external "@mui/utils/unsupportedProp" ***!
  \*********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/unsupportedProp");

/***/ }),

/***/ "@mui/utils/useControlled":
/*!*******************************************!*\
  !*** external "@mui/utils/useControlled" ***!
  \*******************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/useControlled");

/***/ }),

/***/ "@mui/utils/useEnhancedEffect":
/*!***********************************************!*\
  !*** external "@mui/utils/useEnhancedEffect" ***!
  \***********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/useEnhancedEffect");

/***/ }),

/***/ "@mui/utils/useEventCallback":
/*!**********************************************!*\
  !*** external "@mui/utils/useEventCallback" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/useEventCallback");

/***/ }),

/***/ "@mui/utils/useForkRef":
/*!****************************************!*\
  !*** external "@mui/utils/useForkRef" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/useForkRef");

/***/ }),

/***/ "@mui/utils/useId":
/*!***********************************!*\
  !*** external "@mui/utils/useId" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("@mui/utils/useId");

/***/ }),

/***/ "@mui/utils/useIsFocusVisible":
/*!***********************************************!*\
  !*** external "@mui/utils/useIsFocusVisible" ***!
  \***********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/useIsFocusVisible");

/***/ }),

/***/ "@mui/utils/useSlotProps":
/*!******************************************!*\
  !*** external "@mui/utils/useSlotProps" ***!
  \******************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/useSlotProps");

/***/ }),

/***/ "@mui/utils/useTimeout":
/*!****************************************!*\
  !*** external "@mui/utils/useTimeout" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/useTimeout");

/***/ }),

/***/ "@popperjs/core":
/*!*********************************!*\
  !*** external "@popperjs/core" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@popperjs/core");

/***/ }),

/***/ "clsx?ce27":
/*!***********************!*\
  !*** external "clsx" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("clsx");

/***/ }),

/***/ "eventemitter3":
/*!********************************!*\
  !*** external "eventemitter3" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("eventemitter3");

/***/ }),

/***/ "lodash/every":
/*!*******************************!*\
  !*** external "lodash/every" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("lodash/every");

/***/ }),

/***/ "lodash/find":
/*!******************************!*\
  !*** external "lodash/find" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("lodash/find");

/***/ }),

/***/ "lodash/flatMap":
/*!*********************************!*\
  !*** external "lodash/flatMap" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("lodash/flatMap");

/***/ }),

/***/ "lodash/get":
/*!*****************************!*\
  !*** external "lodash/get" ***!
  \*****************************/
/***/ ((module) => {

module.exports = require("lodash/get");

/***/ }),

/***/ "lodash/isBoolean":
/*!***********************************!*\
  !*** external "lodash/isBoolean" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("lodash/isBoolean");

/***/ }),

/***/ "lodash/isEqual":
/*!*********************************!*\
  !*** external "lodash/isEqual" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("lodash/isEqual");

/***/ }),

/***/ "lodash/isFunction":
/*!************************************!*\
  !*** external "lodash/isFunction" ***!
  \************************************/
/***/ ((module) => {

module.exports = require("lodash/isFunction");

/***/ }),

/***/ "lodash/isNaN":
/*!*******************************!*\
  !*** external "lodash/isNaN" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("lodash/isNaN");

/***/ }),

/***/ "lodash/isNil":
/*!*******************************!*\
  !*** external "lodash/isNil" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("lodash/isNil");

/***/ }),

/***/ "lodash/isNumber":
/*!**********************************!*\
  !*** external "lodash/isNumber" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("lodash/isNumber");

/***/ }),

/***/ "lodash/isObject":
/*!**********************************!*\
  !*** external "lodash/isObject" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("lodash/isObject");

/***/ }),

/***/ "lodash/isPlainObject":
/*!***************************************!*\
  !*** external "lodash/isPlainObject" ***!
  \***************************************/
/***/ ((module) => {

module.exports = require("lodash/isPlainObject");

/***/ }),

/***/ "lodash/isString":
/*!**********************************!*\
  !*** external "lodash/isString" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("lodash/isString");

/***/ }),

/***/ "lodash/last":
/*!******************************!*\
  !*** external "lodash/last" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("lodash/last");

/***/ }),

/***/ "lodash/mapValues":
/*!***********************************!*\
  !*** external "lodash/mapValues" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("lodash/mapValues");

/***/ }),

/***/ "lodash/max":
/*!*****************************!*\
  !*** external "lodash/max" ***!
  \*****************************/
/***/ ((module) => {

module.exports = require("lodash/max");

/***/ }),

/***/ "lodash/memoize":
/*!*********************************!*\
  !*** external "lodash/memoize" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("lodash/memoize");

/***/ }),

/***/ "lodash/min":
/*!*****************************!*\
  !*** external "lodash/min" ***!
  \*****************************/
/***/ ((module) => {

module.exports = require("lodash/min");

/***/ }),

/***/ "lodash/range":
/*!*******************************!*\
  !*** external "lodash/range" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("lodash/range");

/***/ }),

/***/ "lodash/some":
/*!******************************!*\
  !*** external "lodash/some" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("lodash/some");

/***/ }),

/***/ "lodash/sortBy":
/*!********************************!*\
  !*** external "lodash/sortBy" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("lodash/sortBy");

/***/ }),

/***/ "lodash/throttle":
/*!**********************************!*\
  !*** external "lodash/throttle" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("lodash/throttle");

/***/ }),

/***/ "lodash/uniqBy":
/*!********************************!*\
  !*** external "lodash/uniqBy" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("lodash/uniqBy");

/***/ }),

/***/ "lodash/upperFirst":
/*!************************************!*\
  !*** external "lodash/upperFirst" ***!
  \************************************/
/***/ ((module) => {

module.exports = require("lodash/upperFirst");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next/head");

/***/ }),

/***/ "prop-types":
/*!*****************************!*\
  !*** external "prop-types" ***!
  \*****************************/
/***/ ((module) => {

module.exports = require("prop-types");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("react-dom");

/***/ }),

/***/ "react-smooth":
/*!*******************************!*\
  !*** external "react-smooth" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("react-smooth");

/***/ }),

/***/ "react-transition-group":
/*!*****************************************!*\
  !*** external "react-transition-group" ***!
  \*****************************************/
/***/ ((module) => {

module.exports = require("react-transition-group");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "recharts-scale":
/*!*********************************!*\
  !*** external "recharts-scale" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("recharts-scale");

/***/ }),

/***/ "victory-vendor/d3-scale":
/*!******************************************!*\
  !*** external "victory-vendor/d3-scale" ***!
  \******************************************/
/***/ ((module) => {

module.exports = require("victory-vendor/d3-scale");

/***/ }),

/***/ "victory-vendor/d3-shape":
/*!******************************************!*\
  !*** external "victory-vendor/d3-shape" ***!
  \******************************************/
/***/ ((module) => {

module.exports = require("victory-vendor/d3-shape");

/***/ }),

/***/ "@emotion/cache":
/*!*********************************!*\
  !*** external "@emotion/cache" ***!
  \*********************************/
/***/ ((module) => {

module.exports = import("@emotion/cache");;

/***/ }),

/***/ "@emotion/react":
/*!*********************************!*\
  !*** external "@emotion/react" ***!
  \*********************************/
/***/ ((module) => {

module.exports = import("@emotion/react");;

/***/ }),

/***/ "@emotion/server/create-instance":
/*!**************************************************!*\
  !*** external "@emotion/server/create-instance" ***!
  \**************************************************/
/***/ ((module) => {

module.exports = import("@emotion/server/create-instance");;

/***/ }),

/***/ "@tanstack/react-query":
/*!****************************************!*\
  !*** external "@tanstack/react-query" ***!
  \****************************************/
/***/ ((module) => {

module.exports = import("@tanstack/react-query");;

/***/ }),

/***/ "axios":
/*!************************!*\
  !*** external "axios" ***!
  \************************/
/***/ ((module) => {

module.exports = import("axios");;

/***/ }),

/***/ "clsx?9dfb":
/*!***********************!*\
  !*** external "clsx" ***!
  \***********************/
/***/ ((module) => {

module.exports = import("clsx");;

/***/ }),

/***/ "react-hot-toast":
/*!**********************************!*\
  !*** external "react-hot-toast" ***!
  \**********************************/
/***/ ((module) => {

module.exports = import("react-hot-toast");;

/***/ }),

/***/ "tiny-invariant":
/*!*********************************!*\
  !*** external "tiny-invariant" ***!
  \*********************************/
/***/ ((module) => {

module.exports = import("tiny-invariant");;

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@mui","vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@babel"], () => (__webpack_exec__("../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();