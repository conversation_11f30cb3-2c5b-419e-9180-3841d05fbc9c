"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_app";
exports.ids = ["pages/_app"];
exports.modules = {

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MyApp)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _emotion_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @emotion/react */ \"@emotion/react\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/material/styles */ \"@mui/material/styles\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_mui_material_styles__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mui/material/CssBaseline */ \"@mui/material/CssBaseline\");\n/* harmony import */ var _mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tanstack/react-query */ \"@tanstack/react-query\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\n/* harmony import */ var _utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../utils/createEmotionCache */ \"./utils/createEmotionCache.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_emotion_react__WEBPACK_IMPORTED_MODULE_3__, _tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__, react_hot_toast__WEBPACK_IMPORTED_MODULE_7__, _utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_8__]);\n([_emotion_react__WEBPACK_IMPORTED_MODULE_3__, _tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__, react_hot_toast__WEBPACK_IMPORTED_MODULE_7__, _utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n// Client-side cache, shared for the whole session of the user in the browser.\nconst clientSideEmotionCache = (0,_utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_8__.createEmotionCache)();\n// Create a client for React Query\nconst queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__.QueryClient({\n    defaultOptions: {\n        queries: {\n            refetchOnWindowFocus: false,\n            retry: 1,\n            staleTime: 5 * 60 * 1000\n        }\n    }\n});\n// Material Design 3 inspired theme\nconst theme = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_4__.createTheme)({\n    palette: {\n        mode: \"light\",\n        primary: {\n            main: \"#1976d2\",\n            light: \"#42a5f5\",\n            dark: \"#1565c0\",\n            contrastText: \"#ffffff\"\n        },\n        secondary: {\n            main: \"#dc004e\",\n            light: \"#ff5983\",\n            dark: \"#9a0036\",\n            contrastText: \"#ffffff\"\n        },\n        success: {\n            main: \"#2e7d32\",\n            light: \"#4caf50\",\n            dark: \"#1b5e20\"\n        },\n        warning: {\n            main: \"#ed6c02\",\n            light: \"#ff9800\",\n            dark: \"#e65100\"\n        },\n        error: {\n            main: \"#d32f2f\",\n            light: \"#f44336\",\n            dark: \"#c62828\"\n        },\n        info: {\n            main: \"#0288d1\",\n            light: \"#03a9f4\",\n            dark: \"#01579b\"\n        },\n        background: {\n            default: \"#fafafa\",\n            paper: \"#ffffff\"\n        },\n        text: {\n            primary: \"rgba(0, 0, 0, 0.87)\",\n            secondary: \"rgba(0, 0, 0, 0.6)\"\n        }\n    },\n    typography: {\n        fontFamily: '\"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n        h1: {\n            fontWeight: 600,\n            fontSize: \"2.5rem\",\n            lineHeight: 1.2\n        },\n        h2: {\n            fontWeight: 600,\n            fontSize: \"2rem\",\n            lineHeight: 1.3\n        },\n        h3: {\n            fontWeight: 600,\n            fontSize: \"1.75rem\",\n            lineHeight: 1.4\n        },\n        h4: {\n            fontWeight: 600,\n            fontSize: \"1.5rem\",\n            lineHeight: 1.4\n        },\n        h5: {\n            fontWeight: 600,\n            fontSize: \"1.25rem\",\n            lineHeight: 1.5\n        },\n        h6: {\n            fontWeight: 600,\n            fontSize: \"1rem\",\n            lineHeight: 1.6\n        },\n        body1: {\n            fontSize: \"1rem\",\n            lineHeight: 1.5\n        },\n        body2: {\n            fontSize: \"0.875rem\",\n            lineHeight: 1.43\n        }\n    },\n    shape: {\n        borderRadius: 12\n    },\n    shadows: [\n        \"none\",\n        \"0px 2px 1px -1px rgba(0,0,0,0.2),0px 1px 1px 0px rgba(0,0,0,0.14),0px 1px 3px 0px rgba(0,0,0,0.12)\",\n        \"0px 3px 1px -2px rgba(0,0,0,0.2),0px 2px 2px 0px rgba(0,0,0,0.14),0px 1px 5px 0px rgba(0,0,0,0.12)\",\n        \"0px 3px 3px -2px rgba(0,0,0,0.2),0px 3px 4px 0px rgba(0,0,0,0.14),0px 1px 8px 0px rgba(0,0,0,0.12)\",\n        \"0px 2px 4px -1px rgba(0,0,0,0.2),0px 4px 5px 0px rgba(0,0,0,0.14),0px 1px 10px 0px rgba(0,0,0,0.12)\",\n        \"0px 3px 5px -1px rgba(0,0,0,0.2),0px 5px 8px 0px rgba(0,0,0,0.14),0px 1px 14px 0px rgba(0,0,0,0.12)\",\n        \"0px 3px 5px -1px rgba(0,0,0,0.2),0px 6px 10px 0px rgba(0,0,0,0.14),0px 1px 18px 0px rgba(0,0,0,0.12)\",\n        \"0px 4px 5px -2px rgba(0,0,0,0.2),0px 7px 10px 1px rgba(0,0,0,0.14),0px 2px 16px 1px rgba(0,0,0,0.12)\",\n        \"0px 5px 5px -3px rgba(0,0,0,0.2),0px 8px 10px 1px rgba(0,0,0,0.14),0px 3px 14px 2px rgba(0,0,0,0.12)\",\n        \"0px 5px 6px -3px rgba(0,0,0,0.2),0px 9px 12px 1px rgba(0,0,0,0.14),0px 3px 16px 2px rgba(0,0,0,0.12)\",\n        \"0px 6px 6px -3px rgba(0,0,0,0.2),0px 10px 14px 1px rgba(0,0,0,0.14),0px 4px 18px 3px rgba(0,0,0,0.12)\",\n        \"0px 6px 7px -4px rgba(0,0,0,0.2),0px 11px 15px 1px rgba(0,0,0,0.14),0px 4px 20px 3px rgba(0,0,0,0.12)\",\n        \"0px 7px 8px -4px rgba(0,0,0,0.2),0px 12px 17px 2px rgba(0,0,0,0.14),0px 5px 22px 4px rgba(0,0,0,0.12)\",\n        \"0px 7px 8px -4px rgba(0,0,0,0.2),0px 13px 19px 2px rgba(0,0,0,0.14),0px 5px 24px 4px rgba(0,0,0,0.12)\",\n        \"0px 7px 9px -4px rgba(0,0,0,0.2),0px 14px 21px 2px rgba(0,0,0,0.14),0px 5px 26px 4px rgba(0,0,0,0.12)\",\n        \"0px 8px 9px -5px rgba(0,0,0,0.2),0px 15px 22px 2px rgba(0,0,0,0.14),0px 6px 28px 5px rgba(0,0,0,0.12)\",\n        \"0px 8px 10px -5px rgba(0,0,0,0.2),0px 16px 24px 2px rgba(0,0,0,0.14),0px 6px 30px 5px rgba(0,0,0,0.12)\",\n        \"0px 8px 11px -5px rgba(0,0,0,0.2),0px 17px 26px 2px rgba(0,0,0,0.14),0px 6px 32px 5px rgba(0,0,0,0.12)\",\n        \"0px 9px 11px -5px rgba(0,0,0,0.2),0px 18px 28px 2px rgba(0,0,0,0.14),0px 7px 34px 6px rgba(0,0,0,0.12)\",\n        \"0px 9px 12px -6px rgba(0,0,0,0.2),0px 19px 29px 2px rgba(0,0,0,0.14),0px 7px 36px 6px rgba(0,0,0,0.12)\",\n        \"0px 10px 13px -6px rgba(0,0,0,0.2),0px 20px 31px 3px rgba(0,0,0,0.14),0px 8px 38px 7px rgba(0,0,0,0.12)\",\n        \"0px 10px 13px -6px rgba(0,0,0,0.2),0px 21px 33px 3px rgba(0,0,0,0.14),0px 8px 40px 7px rgba(0,0,0,0.12)\",\n        \"0px 10px 14px -6px rgba(0,0,0,0.2),0px 22px 35px 3px rgba(0,0,0,0.14),0px 8px 42px 7px rgba(0,0,0,0.12)\",\n        \"0px 11px 14px -7px rgba(0,0,0,0.2),0px 23px 36px 3px rgba(0,0,0,0.14),0px 9px 44px 8px rgba(0,0,0,0.12)\",\n        \"0px 11px 15px -7px rgba(0,0,0,0.2),0px 24px 38px 3px rgba(0,0,0,0.14),0px 9px 46px 8px rgba(0,0,0,0.12)\"\n    ],\n    components: {\n        MuiCard: {\n            styleOverrides: {\n                root: {\n                    borderRadius: 16,\n                    boxShadow: \"0px 2px 8px rgba(0, 0, 0, 0.1)\",\n                    transition: \"box-shadow 0.3s ease-in-out, transform 0.2s ease-in-out\",\n                    \"&:hover\": {\n                        boxShadow: \"0px 4px 16px rgba(0, 0, 0, 0.15)\",\n                        transform: \"translateY(-2px)\"\n                    }\n                }\n            }\n        },\n        MuiButton: {\n            styleOverrides: {\n                root: {\n                    borderRadius: 12,\n                    textTransform: \"none\",\n                    fontWeight: 500,\n                    padding: \"8px 24px\",\n                    transition: \"all 0.2s ease-in-out\",\n                    \"&:hover\": {\n                        transform: \"translateY(-1px)\",\n                        boxShadow: \"0px 4px 12px rgba(0, 0, 0, 0.15)\"\n                    }\n                },\n                contained: {\n                    boxShadow: \"0px 2px 8px rgba(0, 0, 0, 0.15)\"\n                }\n            }\n        },\n        MuiChip: {\n            styleOverrides: {\n                root: {\n                    borderRadius: 8,\n                    fontWeight: 500\n                }\n            }\n        },\n        MuiPaper: {\n            styleOverrides: {\n                root: {\n                    borderRadius: 12\n                },\n                elevation1: {\n                    boxShadow: \"0px 2px 8px rgba(0, 0, 0, 0.1)\"\n                }\n            }\n        },\n        MuiAppBar: {\n            styleOverrides: {\n                root: {\n                    boxShadow: \"0px 2px 8px rgba(0, 0, 0, 0.1)\"\n                }\n            }\n        }\n    }\n});\nfunction MyApp(props) {\n    const { Component, emotionCache = clientSideEmotionCache, pageProps } = props;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Register service worker for PWA\n        if (\"serviceWorker\" in navigator) {\n            navigator.serviceWorker.register(\"/sw.js\").then((registration)=>{\n                console.log(\"SW registered: \", registration);\n            }).catch((registrationError)=>{\n                console.log(\"SW registration failed: \", registrationError);\n            });\n        }\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_emotion_react__WEBPACK_IMPORTED_MODULE_3__.CacheProvider, {\n        value: emotionCache,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"initial-scale=1, width=device-width\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_app.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Solana Trading Intelligence System\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_app.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_app.tsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__.QueryClientProvider, {\n                client: queryClient,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_styles__WEBPACK_IMPORTED_MODULE_4__.ThemeProvider, {\n                    theme: theme,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_5___default()), {}, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_app.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                            ...pageProps\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_app.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_7__.Toaster, {\n                            position: \"top-right\",\n                            toastOptions: {\n                                duration: 4000,\n                                style: {\n                                    background: \"#363636\",\n                                    color: \"#fff\",\n                                    borderRadius: \"12px\"\n                                },\n                                success: {\n                                    style: {\n                                        background: \"#2e7d32\"\n                                    }\n                                },\n                                error: {\n                                    style: {\n                                        background: \"#d32f2f\"\n                                    }\n                                }\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_app.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_app.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_app.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_app.tsx\",\n        lineNumber: 223,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./utils/createEmotionCache.ts":
/*!*************************************!*\
  !*** ./utils/createEmotionCache.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createEmotionCache: () => (/* binding */ createEmotionCache),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _emotion_cache__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @emotion/cache */ \"@emotion/cache\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_emotion_cache__WEBPACK_IMPORTED_MODULE_0__]);\n_emotion_cache__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nconst isBrowser = typeof document !== \"undefined\";\n// On the client side, Create a meta tag at the top of the <head> and set it as insertionPoint.\n// This assures that MUI styles are loaded first.\n// It allows developers to easily override MUI styles with other styling solutions, like CSS modules.\nfunction createEmotionCache() {\n    let insertionPoint;\n    if (isBrowser) {\n        const emotionInsertionPoint = document.querySelector('meta[name=\"emotion-insertion-point\"]');\n        insertionPoint = emotionInsertionPoint ?? undefined;\n    }\n    return (0,_emotion_cache__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n        key: \"mui-style\",\n        insertionPoint\n    });\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (createEmotionCache);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi91dGlscy9jcmVhdGVFbW90aW9uQ2FjaGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQXlDO0FBRXpDLE1BQU1DLFlBQVksT0FBT0MsYUFBYTtBQUV0QywrRkFBK0Y7QUFDL0YsaURBQWlEO0FBQ2pELHFHQUFxRztBQUNyRyxTQUFTQztJQUNQLElBQUlDO0lBRUosSUFBSUgsV0FBVztRQUNiLE1BQU1JLHdCQUF3QkgsU0FBU0ksYUFBYSxDQUNsRDtRQUVGRixpQkFBaUJDLHlCQUF5QkU7SUFDNUM7SUFFQSxPQUFPUCwwREFBV0EsQ0FBQztRQUFFUSxLQUFLO1FBQWFKO0lBQWU7QUFDeEQ7QUFFOEI7QUFDOUIsaUVBQWVELGtCQUFrQkEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL3NvbGFuYS10cmFkaW5nLWRhc2hib2FyZC8uL3V0aWxzL2NyZWF0ZUVtb3Rpb25DYWNoZS50cz8xY2U2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVDYWNoZSBmcm9tICdAZW1vdGlvbi9jYWNoZSc7XG5cbmNvbnN0IGlzQnJvd3NlciA9IHR5cGVvZiBkb2N1bWVudCAhPT0gJ3VuZGVmaW5lZCc7XG5cbi8vIE9uIHRoZSBjbGllbnQgc2lkZSwgQ3JlYXRlIGEgbWV0YSB0YWcgYXQgdGhlIHRvcCBvZiB0aGUgPGhlYWQ+IGFuZCBzZXQgaXQgYXMgaW5zZXJ0aW9uUG9pbnQuXG4vLyBUaGlzIGFzc3VyZXMgdGhhdCBNVUkgc3R5bGVzIGFyZSBsb2FkZWQgZmlyc3QuXG4vLyBJdCBhbGxvd3MgZGV2ZWxvcGVycyB0byBlYXNpbHkgb3ZlcnJpZGUgTVVJIHN0eWxlcyB3aXRoIG90aGVyIHN0eWxpbmcgc29sdXRpb25zLCBsaWtlIENTUyBtb2R1bGVzLlxuZnVuY3Rpb24gY3JlYXRlRW1vdGlvbkNhY2hlKCkge1xuICBsZXQgaW5zZXJ0aW9uUG9pbnQ7XG5cbiAgaWYgKGlzQnJvd3Nlcikge1xuICAgIGNvbnN0IGVtb3Rpb25JbnNlcnRpb25Qb2ludCA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3I8SFRNTE1ldGFFbGVtZW50PihcbiAgICAgICdtZXRhW25hbWU9XCJlbW90aW9uLWluc2VydGlvbi1wb2ludFwiXScsXG4gICAgKTtcbiAgICBpbnNlcnRpb25Qb2ludCA9IGVtb3Rpb25JbnNlcnRpb25Qb2ludCA/PyB1bmRlZmluZWQ7XG4gIH1cblxuICByZXR1cm4gY3JlYXRlQ2FjaGUoeyBrZXk6ICdtdWktc3R5bGUnLCBpbnNlcnRpb25Qb2ludCB9KTtcbn1cblxuZXhwb3J0IHsgY3JlYXRlRW1vdGlvbkNhY2hlIH07XG5leHBvcnQgZGVmYXVsdCBjcmVhdGVFbW90aW9uQ2FjaGU7XG4iXSwibmFtZXMiOlsiY3JlYXRlQ2FjaGUiLCJpc0Jyb3dzZXIiLCJkb2N1bWVudCIsImNyZWF0ZUVtb3Rpb25DYWNoZSIsImluc2VydGlvblBvaW50IiwiZW1vdGlvbkluc2VydGlvblBvaW50IiwicXVlcnlTZWxlY3RvciIsInVuZGVmaW5lZCIsImtleSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./utils/createEmotionCache.ts\n");

/***/ }),

/***/ "@mui/material/CssBaseline":
/*!********************************************!*\
  !*** external "@mui/material/CssBaseline" ***!
  \********************************************/
/***/ ((module) => {

module.exports = require("@mui/material/CssBaseline");

/***/ }),

/***/ "@mui/material/styles":
/*!***************************************!*\
  !*** external "@mui/material/styles" ***!
  \***************************************/
/***/ ((module) => {

module.exports = require("@mui/material/styles");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "@emotion/cache":
/*!*********************************!*\
  !*** external "@emotion/cache" ***!
  \*********************************/
/***/ ((module) => {

module.exports = import("@emotion/cache");;

/***/ }),

/***/ "@emotion/react":
/*!*********************************!*\
  !*** external "@emotion/react" ***!
  \*********************************/
/***/ ((module) => {

module.exports = import("@emotion/react");;

/***/ }),

/***/ "@tanstack/react-query":
/*!****************************************!*\
  !*** external "@tanstack/react-query" ***!
  \****************************************/
/***/ ((module) => {

module.exports = import("@tanstack/react-query");;

/***/ }),

/***/ "react-hot-toast":
/*!**********************************!*\
  !*** external "react-hot-toast" ***!
  \**********************************/
/***/ ((module) => {

module.exports = import("react-hot-toast");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./pages/_app.tsx"));
module.exports = __webpack_exports__;

})();