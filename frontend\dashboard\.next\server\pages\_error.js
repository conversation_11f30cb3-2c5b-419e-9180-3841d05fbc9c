"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_error";
exports.ids = ["pages/_error"];
exports.modules = {

/***/ "../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"../../node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"../../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./pages/_document.tsx\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n/* harmony import */ var private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! private-next-pages/_error */ \"../../node_modules/next/dist/pages/_error.js\");\n/* harmony import */ var private_next_pages_error__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__, private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__]);\n([private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__, private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/_error\",\n        pathname: \"/_error\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MyApp)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _emotion_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @emotion/react */ \"@emotion/react\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/material/styles */ \"@mui/material/styles\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_mui_material_styles__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mui/material/CssBaseline */ \"@mui/material/CssBaseline\");\n/* harmony import */ var _mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tanstack/react-query */ \"@tanstack/react-query\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\n/* harmony import */ var _utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../utils/createEmotionCache */ \"./utils/createEmotionCache.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_emotion_react__WEBPACK_IMPORTED_MODULE_3__, _tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__, react_hot_toast__WEBPACK_IMPORTED_MODULE_7__, _utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_8__]);\n([_emotion_react__WEBPACK_IMPORTED_MODULE_3__, _tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__, react_hot_toast__WEBPACK_IMPORTED_MODULE_7__, _utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n// Client-side cache, shared for the whole session of the user in the browser.\nconst clientSideEmotionCache = (0,_utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_8__.createEmotionCache)();\n// Create a client for React Query\nconst queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__.QueryClient({\n    defaultOptions: {\n        queries: {\n            refetchOnWindowFocus: false,\n            retry: 1,\n            staleTime: 5 * 60 * 1000\n        }\n    }\n});\n// Material Design 3 inspired theme\nconst theme = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_4__.createTheme)({\n    palette: {\n        mode: \"light\",\n        primary: {\n            main: \"#1976d2\",\n            light: \"#42a5f5\",\n            dark: \"#1565c0\",\n            contrastText: \"#ffffff\"\n        },\n        secondary: {\n            main: \"#dc004e\",\n            light: \"#ff5983\",\n            dark: \"#9a0036\",\n            contrastText: \"#ffffff\"\n        },\n        success: {\n            main: \"#2e7d32\",\n            light: \"#4caf50\",\n            dark: \"#1b5e20\"\n        },\n        warning: {\n            main: \"#ed6c02\",\n            light: \"#ff9800\",\n            dark: \"#e65100\"\n        },\n        error: {\n            main: \"#d32f2f\",\n            light: \"#f44336\",\n            dark: \"#c62828\"\n        },\n        info: {\n            main: \"#0288d1\",\n            light: \"#03a9f4\",\n            dark: \"#01579b\"\n        },\n        background: {\n            default: \"#fafafa\",\n            paper: \"#ffffff\"\n        },\n        text: {\n            primary: \"rgba(0, 0, 0, 0.87)\",\n            secondary: \"rgba(0, 0, 0, 0.6)\"\n        }\n    },\n    typography: {\n        fontFamily: '\"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n        h1: {\n            fontWeight: 600,\n            fontSize: \"2.5rem\",\n            lineHeight: 1.2\n        },\n        h2: {\n            fontWeight: 600,\n            fontSize: \"2rem\",\n            lineHeight: 1.3\n        },\n        h3: {\n            fontWeight: 600,\n            fontSize: \"1.75rem\",\n            lineHeight: 1.4\n        },\n        h4: {\n            fontWeight: 600,\n            fontSize: \"1.5rem\",\n            lineHeight: 1.4\n        },\n        h5: {\n            fontWeight: 600,\n            fontSize: \"1.25rem\",\n            lineHeight: 1.5\n        },\n        h6: {\n            fontWeight: 600,\n            fontSize: \"1rem\",\n            lineHeight: 1.6\n        },\n        body1: {\n            fontSize: \"1rem\",\n            lineHeight: 1.5\n        },\n        body2: {\n            fontSize: \"0.875rem\",\n            lineHeight: 1.43\n        }\n    },\n    shape: {\n        borderRadius: 12\n    },\n    shadows: [\n        \"none\",\n        \"0px 2px 1px -1px rgba(0,0,0,0.2),0px 1px 1px 0px rgba(0,0,0,0.14),0px 1px 3px 0px rgba(0,0,0,0.12)\",\n        \"0px 3px 1px -2px rgba(0,0,0,0.2),0px 2px 2px 0px rgba(0,0,0,0.14),0px 1px 5px 0px rgba(0,0,0,0.12)\",\n        \"0px 3px 3px -2px rgba(0,0,0,0.2),0px 3px 4px 0px rgba(0,0,0,0.14),0px 1px 8px 0px rgba(0,0,0,0.12)\",\n        \"0px 2px 4px -1px rgba(0,0,0,0.2),0px 4px 5px 0px rgba(0,0,0,0.14),0px 1px 10px 0px rgba(0,0,0,0.12)\",\n        \"0px 3px 5px -1px rgba(0,0,0,0.2),0px 5px 8px 0px rgba(0,0,0,0.14),0px 1px 14px 0px rgba(0,0,0,0.12)\",\n        \"0px 3px 5px -1px rgba(0,0,0,0.2),0px 6px 10px 0px rgba(0,0,0,0.14),0px 1px 18px 0px rgba(0,0,0,0.12)\",\n        \"0px 4px 5px -2px rgba(0,0,0,0.2),0px 7px 10px 1px rgba(0,0,0,0.14),0px 2px 16px 1px rgba(0,0,0,0.12)\",\n        \"0px 5px 5px -3px rgba(0,0,0,0.2),0px 8px 10px 1px rgba(0,0,0,0.14),0px 3px 14px 2px rgba(0,0,0,0.12)\",\n        \"0px 5px 6px -3px rgba(0,0,0,0.2),0px 9px 12px 1px rgba(0,0,0,0.14),0px 3px 16px 2px rgba(0,0,0,0.12)\",\n        \"0px 6px 6px -3px rgba(0,0,0,0.2),0px 10px 14px 1px rgba(0,0,0,0.14),0px 4px 18px 3px rgba(0,0,0,0.12)\",\n        \"0px 6px 7px -4px rgba(0,0,0,0.2),0px 11px 15px 1px rgba(0,0,0,0.14),0px 4px 20px 3px rgba(0,0,0,0.12)\",\n        \"0px 7px 8px -4px rgba(0,0,0,0.2),0px 12px 17px 2px rgba(0,0,0,0.14),0px 5px 22px 4px rgba(0,0,0,0.12)\",\n        \"0px 7px 8px -4px rgba(0,0,0,0.2),0px 13px 19px 2px rgba(0,0,0,0.14),0px 5px 24px 4px rgba(0,0,0,0.12)\",\n        \"0px 7px 9px -4px rgba(0,0,0,0.2),0px 14px 21px 2px rgba(0,0,0,0.14),0px 5px 26px 4px rgba(0,0,0,0.12)\",\n        \"0px 8px 9px -5px rgba(0,0,0,0.2),0px 15px 22px 2px rgba(0,0,0,0.14),0px 6px 28px 5px rgba(0,0,0,0.12)\",\n        \"0px 8px 10px -5px rgba(0,0,0,0.2),0px 16px 24px 2px rgba(0,0,0,0.14),0px 6px 30px 5px rgba(0,0,0,0.12)\",\n        \"0px 8px 11px -5px rgba(0,0,0,0.2),0px 17px 26px 2px rgba(0,0,0,0.14),0px 6px 32px 5px rgba(0,0,0,0.12)\",\n        \"0px 9px 11px -5px rgba(0,0,0,0.2),0px 18px 28px 2px rgba(0,0,0,0.14),0px 7px 34px 6px rgba(0,0,0,0.12)\",\n        \"0px 9px 12px -6px rgba(0,0,0,0.2),0px 19px 29px 2px rgba(0,0,0,0.14),0px 7px 36px 6px rgba(0,0,0,0.12)\",\n        \"0px 10px 13px -6px rgba(0,0,0,0.2),0px 20px 31px 3px rgba(0,0,0,0.14),0px 8px 38px 7px rgba(0,0,0,0.12)\",\n        \"0px 10px 13px -6px rgba(0,0,0,0.2),0px 21px 33px 3px rgba(0,0,0,0.14),0px 8px 40px 7px rgba(0,0,0,0.12)\",\n        \"0px 10px 14px -6px rgba(0,0,0,0.2),0px 22px 35px 3px rgba(0,0,0,0.14),0px 8px 42px 7px rgba(0,0,0,0.12)\",\n        \"0px 11px 14px -7px rgba(0,0,0,0.2),0px 23px 36px 3px rgba(0,0,0,0.14),0px 9px 44px 8px rgba(0,0,0,0.12)\",\n        \"0px 11px 15px -7px rgba(0,0,0,0.2),0px 24px 38px 3px rgba(0,0,0,0.14),0px 9px 46px 8px rgba(0,0,0,0.12)\"\n    ],\n    components: {\n        MuiCard: {\n            styleOverrides: {\n                root: {\n                    borderRadius: 16,\n                    boxShadow: \"0px 2px 8px rgba(0, 0, 0, 0.1)\",\n                    transition: \"box-shadow 0.3s ease-in-out, transform 0.2s ease-in-out\",\n                    \"&:hover\": {\n                        boxShadow: \"0px 4px 16px rgba(0, 0, 0, 0.15)\",\n                        transform: \"translateY(-2px)\"\n                    }\n                }\n            }\n        },\n        MuiButton: {\n            styleOverrides: {\n                root: {\n                    borderRadius: 12,\n                    textTransform: \"none\",\n                    fontWeight: 500,\n                    padding: \"8px 24px\",\n                    transition: \"all 0.2s ease-in-out\",\n                    \"&:hover\": {\n                        transform: \"translateY(-1px)\",\n                        boxShadow: \"0px 4px 12px rgba(0, 0, 0, 0.15)\"\n                    }\n                },\n                contained: {\n                    boxShadow: \"0px 2px 8px rgba(0, 0, 0, 0.15)\"\n                }\n            }\n        },\n        MuiChip: {\n            styleOverrides: {\n                root: {\n                    borderRadius: 8,\n                    fontWeight: 500\n                }\n            }\n        },\n        MuiPaper: {\n            styleOverrides: {\n                root: {\n                    borderRadius: 12\n                },\n                elevation1: {\n                    boxShadow: \"0px 2px 8px rgba(0, 0, 0, 0.1)\"\n                }\n            }\n        },\n        MuiAppBar: {\n            styleOverrides: {\n                root: {\n                    boxShadow: \"0px 2px 8px rgba(0, 0, 0, 0.1)\"\n                }\n            }\n        }\n    }\n});\nfunction MyApp(props) {\n    const { Component, emotionCache = clientSideEmotionCache, pageProps } = props;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Register service worker for PWA\n        if (\"serviceWorker\" in navigator) {\n            navigator.serviceWorker.register(\"/sw.js\").then((registration)=>{\n                console.log(\"SW registered: \", registration);\n            }).catch((registrationError)=>{\n                console.log(\"SW registration failed: \", registrationError);\n            });\n        }\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_emotion_react__WEBPACK_IMPORTED_MODULE_3__.CacheProvider, {\n        value: emotionCache,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"initial-scale=1, width=device-width\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_app.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Solana Trading Intelligence System\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_app.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_app.tsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__.QueryClientProvider, {\n                client: queryClient,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_styles__WEBPACK_IMPORTED_MODULE_4__.ThemeProvider, {\n                    theme: theme,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_5___default()), {}, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_app.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                            ...pageProps\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_app.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_7__.Toaster, {\n                            position: \"top-right\",\n                            toastOptions: {\n                                duration: 4000,\n                                style: {\n                                    background: \"#363636\",\n                                    color: \"#fff\",\n                                    borderRadius: \"12px\"\n                                },\n                                success: {\n                                    style: {\n                                        background: \"#2e7d32\"\n                                    }\n                                },\n                                error: {\n                                    style: {\n                                        background: \"#d32f2f\"\n                                    }\n                                }\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_app.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_app.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_app.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_app.tsx\",\n        lineNumber: 223,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./pages/_document.tsx":
/*!*****************************!*\
  !*** ./pages/_document.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MyDocument)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/document */ \"../../node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/createEmotionCache */ \"./utils/createEmotionCache.ts\");\n/* harmony import */ var _emotion_server_create_instance__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @emotion/server/create-instance */ \"@emotion/server/create-instance\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_3__, _emotion_server_create_instance__WEBPACK_IMPORTED_MODULE_4__]);\n([_utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_3__, _emotion_server_create_instance__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nclass MyDocument extends (next_document__WEBPACK_IMPORTED_MODULE_2___default()) {\n    static async getInitialProps(ctx) {\n        const originalRenderPage = ctx.renderPage;\n        const cache = (0,_utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_3__.createEmotionCache)();\n        const { extractCriticalToChunks } = (0,_emotion_server_create_instance__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(cache);\n        ctx.renderPage = ()=>originalRenderPage({\n                enhanceApp: (App)=>function EnhanceApp(props) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(App, {\n                            emotionCache: cache,\n                            ...props\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 20\n                        }, this);\n                    }\n            });\n        const initialProps = await next_document__WEBPACK_IMPORTED_MODULE_2___default().getInitialProps(ctx);\n        const emotionStyles = extractCriticalToChunks(initialProps.html);\n        const emotionStyleTags = emotionStyles.styles.map((style)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                \"data-emotion\": `${style.key} ${style.ids.join(\" \")}`,\n                dangerouslySetInnerHTML: {\n                    __html: style.css\n                }\n            }, style.key, false, {\n                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this));\n        return {\n            ...initialProps,\n            emotionStyleTags\n        };\n    }\n    render() {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.Html, {\n            lang: \"en\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.Head, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            charSet: \"utf-8\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"theme-color\",\n                            content: \"#1976d2\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"description\",\n                            content: \"Real-time Solana trading intelligence dashboard with whale tracking, rugpull detection, and advanced analytics\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"keywords\",\n                            content: \"solana, trading, intelligence, whale tracking, rugpull detection, crypto analytics, defi, blockchain\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"author\",\n                            content: \"HectorTa1989\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"robots\",\n                            content: \"index, follow\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            property: \"og:type\",\n                            content: \"website\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            property: \"og:title\",\n                            content: \"Solana Trading Intelligence System\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            property: \"og:description\",\n                            content: \"Real-time Solana trading intelligence dashboard with whale tracking, rugpull detection, and advanced analytics\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            property: \"og:image\",\n                            content: \"/og-image.png\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            property: \"og:url\",\n                            content: \"https://solana-intel.io\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            property: \"og:site_name\",\n                            content: \"Solana Trading Intelligence\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"twitter:card\",\n                            content: \"summary_large_image\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"twitter:title\",\n                            content: \"Solana Trading Intelligence System\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"twitter:description\",\n                            content: \"Real-time Solana trading intelligence dashboard with whale tracking, rugpull detection, and advanced analytics\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"twitter:image\",\n                            content: \"/twitter-image.png\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"twitter:creator\",\n                            content: \"@HectorTa1989\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            href: \"/favicon.ico\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            type: \"image/png\",\n                            sizes: \"32x32\",\n                            href: \"/favicon-32x32.png\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            type: \"image/png\",\n                            sizes: \"16x16\",\n                            href: \"/favicon-16x16.png\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"apple-touch-icon\",\n                            href: \"/apple-touch-icon.png\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"manifest\",\n                            href: \"/manifest.json\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"preconnect\",\n                            href: \"https://fonts.googleapis.com\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"preconnect\",\n                            href: \"https://fonts.gstatic.com\",\n                            crossOrigin: \"anonymous\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"stylesheet\",\n                            href: \"https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700&display=swap\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"stylesheet\",\n                            href: \"https://fonts.googleapis.com/icon?family=Material+Icons&display=swap\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                            type: \"application/ld+json\",\n                            dangerouslySetInnerHTML: {\n                                __html: JSON.stringify({\n                                    \"@context\": \"https://schema.org\",\n                                    \"@type\": \"WebApplication\",\n                                    \"name\": \"Solana Trading Intelligence System\",\n                                    \"description\": \"Real-time Solana trading intelligence dashboard with whale tracking, rugpull detection, and advanced analytics\",\n                                    \"url\": \"https://solana-intel.io\",\n                                    \"applicationCategory\": \"FinanceApplication\",\n                                    \"operatingSystem\": \"Web\",\n                                    \"author\": {\n                                        \"@type\": \"Person\",\n                                        \"name\": \"HectorTa1989\",\n                                        \"url\": \"https://github.com/HectorTa1989\"\n                                    },\n                                    \"offers\": {\n                                        \"@type\": \"Offer\",\n                                        \"price\": \"0\",\n                                        \"priceCurrency\": \"USD\"\n                                    }\n                                })\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"dns-prefetch\",\n                            href: \"//api.solana-intel.io\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"preconnect\",\n                            href: \"https://api.solana-intel.io\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this),\n                        this.props.emotionStyleTags\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.Main, {}, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.NextScript, {}, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n            lineNumber: 38,\n            columnNumber: 7\n        }, this);\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_document.tsx\n");

/***/ }),

/***/ "./utils/createEmotionCache.ts":
/*!*************************************!*\
  !*** ./utils/createEmotionCache.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createEmotionCache: () => (/* binding */ createEmotionCache),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _emotion_cache__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @emotion/cache */ \"@emotion/cache\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_emotion_cache__WEBPACK_IMPORTED_MODULE_0__]);\n_emotion_cache__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nconst isBrowser = typeof document !== \"undefined\";\n// On the client side, Create a meta tag at the top of the <head> and set it as insertionPoint.\n// This assures that MUI styles are loaded first.\n// It allows developers to easily override MUI styles with other styling solutions, like CSS modules.\nfunction createEmotionCache() {\n    let insertionPoint;\n    if (isBrowser) {\n        const emotionInsertionPoint = document.querySelector('meta[name=\"emotion-insertion-point\"]');\n        insertionPoint = emotionInsertionPoint ?? undefined;\n    }\n    return (0,_emotion_cache__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n        key: \"mui-style\",\n        insertionPoint\n    });\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (createEmotionCache);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi91dGlscy9jcmVhdGVFbW90aW9uQ2FjaGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQXlDO0FBRXpDLE1BQU1DLFlBQVksT0FBT0MsYUFBYTtBQUV0QywrRkFBK0Y7QUFDL0YsaURBQWlEO0FBQ2pELHFHQUFxRztBQUNyRyxTQUFTQztJQUNQLElBQUlDO0lBRUosSUFBSUgsV0FBVztRQUNiLE1BQU1JLHdCQUF3QkgsU0FBU0ksYUFBYSxDQUNsRDtRQUVGRixpQkFBaUJDLHlCQUF5QkU7SUFDNUM7SUFFQSxPQUFPUCwwREFBV0EsQ0FBQztRQUFFUSxLQUFLO1FBQWFKO0lBQWU7QUFDeEQ7QUFFOEI7QUFDOUIsaUVBQWVELGtCQUFrQkEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL3NvbGFuYS10cmFkaW5nLWRhc2hib2FyZC8uL3V0aWxzL2NyZWF0ZUVtb3Rpb25DYWNoZS50cz8xY2U2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVDYWNoZSBmcm9tICdAZW1vdGlvbi9jYWNoZSc7XG5cbmNvbnN0IGlzQnJvd3NlciA9IHR5cGVvZiBkb2N1bWVudCAhPT0gJ3VuZGVmaW5lZCc7XG5cbi8vIE9uIHRoZSBjbGllbnQgc2lkZSwgQ3JlYXRlIGEgbWV0YSB0YWcgYXQgdGhlIHRvcCBvZiB0aGUgPGhlYWQ+IGFuZCBzZXQgaXQgYXMgaW5zZXJ0aW9uUG9pbnQuXG4vLyBUaGlzIGFzc3VyZXMgdGhhdCBNVUkgc3R5bGVzIGFyZSBsb2FkZWQgZmlyc3QuXG4vLyBJdCBhbGxvd3MgZGV2ZWxvcGVycyB0byBlYXNpbHkgb3ZlcnJpZGUgTVVJIHN0eWxlcyB3aXRoIG90aGVyIHN0eWxpbmcgc29sdXRpb25zLCBsaWtlIENTUyBtb2R1bGVzLlxuZnVuY3Rpb24gY3JlYXRlRW1vdGlvbkNhY2hlKCkge1xuICBsZXQgaW5zZXJ0aW9uUG9pbnQ7XG5cbiAgaWYgKGlzQnJvd3Nlcikge1xuICAgIGNvbnN0IGVtb3Rpb25JbnNlcnRpb25Qb2ludCA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3I8SFRNTE1ldGFFbGVtZW50PihcbiAgICAgICdtZXRhW25hbWU9XCJlbW90aW9uLWluc2VydGlvbi1wb2ludFwiXScsXG4gICAgKTtcbiAgICBpbnNlcnRpb25Qb2ludCA9IGVtb3Rpb25JbnNlcnRpb25Qb2ludCA/PyB1bmRlZmluZWQ7XG4gIH1cblxuICByZXR1cm4gY3JlYXRlQ2FjaGUoeyBrZXk6ICdtdWktc3R5bGUnLCBpbnNlcnRpb25Qb2ludCB9KTtcbn1cblxuZXhwb3J0IHsgY3JlYXRlRW1vdGlvbkNhY2hlIH07XG5leHBvcnQgZGVmYXVsdCBjcmVhdGVFbW90aW9uQ2FjaGU7XG4iXSwibmFtZXMiOlsiY3JlYXRlQ2FjaGUiLCJpc0Jyb3dzZXIiLCJkb2N1bWVudCIsImNyZWF0ZUVtb3Rpb25DYWNoZSIsImluc2VydGlvblBvaW50IiwiZW1vdGlvbkluc2VydGlvblBvaW50IiwicXVlcnlTZWxlY3RvciIsInVuZGVmaW5lZCIsImtleSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./utils/createEmotionCache.ts\n");

/***/ }),

/***/ "@mui/material/CssBaseline":
/*!********************************************!*\
  !*** external "@mui/material/CssBaseline" ***!
  \********************************************/
/***/ ((module) => {

module.exports = require("@mui/material/CssBaseline");

/***/ }),

/***/ "@mui/material/styles":
/*!***************************************!*\
  !*** external "@mui/material/styles" ***!
  \***************************************/
/***/ ((module) => {

module.exports = require("@mui/material/styles");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "@emotion/cache":
/*!*********************************!*\
  !*** external "@emotion/cache" ***!
  \*********************************/
/***/ ((module) => {

module.exports = import("@emotion/cache");;

/***/ }),

/***/ "@emotion/react":
/*!*********************************!*\
  !*** external "@emotion/react" ***!
  \*********************************/
/***/ ((module) => {

module.exports = import("@emotion/react");;

/***/ }),

/***/ "@emotion/server/create-instance":
/*!**************************************************!*\
  !*** external "@emotion/server/create-instance" ***!
  \**************************************************/
/***/ ((module) => {

module.exports = import("@emotion/server/create-instance");;

/***/ }),

/***/ "@tanstack/react-query":
/*!****************************************!*\
  !*** external "@tanstack/react-query" ***!
  \****************************************/
/***/ ((module) => {

module.exports = import("@tanstack/react-query");;

/***/ }),

/***/ "react-hot-toast":
/*!**********************************!*\
  !*** external "react-hot-toast" ***!
  \**********************************/
/***/ ((module) => {

module.exports = import("react-hot-toast");;

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();