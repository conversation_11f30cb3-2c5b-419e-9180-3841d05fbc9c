"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "components_TradingChart_tsx";
exports.ids = ["components_TradingChart_tsx"];
exports.modules = {

/***/ "__barrel_optimize__?names=Area,AreaChart,CartesianGrid,Legend,Line,ResponsiveContainer,Tooltip,XAxis,YAxis!=!../../node_modules/recharts/es6/index.js":
/*!*************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Legend,Line,ResponsiveContainer,Tooltip,XAxis,YAxis!=!../../node_modules/recharts/es6/index.js ***!
  \*************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Area: () => (/* reexport safe */ _cartesian_Area__WEBPACK_IMPORTED_MODULE_0__.Area),\n/* harmony export */   AreaChart: () => (/* reexport safe */ _chart_AreaChart__WEBPACK_IMPORTED_MODULE_1__.AreaChart),\n/* harmony export */   CartesianGrid: () => (/* reexport safe */ _cartesian_CartesianGrid__WEBPACK_IMPORTED_MODULE_2__.CartesianGrid),\n/* harmony export */   Legend: () => (/* reexport safe */ _component_Legend__WEBPACK_IMPORTED_MODULE_3__.Legend),\n/* harmony export */   Line: () => (/* reexport safe */ _cartesian_Line__WEBPACK_IMPORTED_MODULE_4__.Line),\n/* harmony export */   ResponsiveContainer: () => (/* reexport safe */ _component_ResponsiveContainer__WEBPACK_IMPORTED_MODULE_5__.ResponsiveContainer),\n/* harmony export */   Tooltip: () => (/* reexport safe */ _component_Tooltip__WEBPACK_IMPORTED_MODULE_6__.Tooltip),\n/* harmony export */   XAxis: () => (/* reexport safe */ _cartesian_XAxis__WEBPACK_IMPORTED_MODULE_7__.XAxis),\n/* harmony export */   YAxis: () => (/* reexport safe */ _cartesian_YAxis__WEBPACK_IMPORTED_MODULE_8__.YAxis)\n/* harmony export */ });\n/* harmony import */ var _cartesian_Area__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./cartesian/Area */ \"../../node_modules/recharts/es6/cartesian/Area.js\");\n/* harmony import */ var _chart_AreaChart__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chart/AreaChart */ \"../../node_modules/recharts/es6/chart/AreaChart.js\");\n/* harmony import */ var _cartesian_CartesianGrid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./cartesian/CartesianGrid */ \"../../node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _component_Legend__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./component/Legend */ \"../../node_modules/recharts/es6/component/Legend.js\");\n/* harmony import */ var _cartesian_Line__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./cartesian/Line */ \"../../node_modules/recharts/es6/cartesian/Line.js\");\n/* harmony import */ var _component_ResponsiveContainer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./component/ResponsiveContainer */ \"../../node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _component_Tooltip__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./component/Tooltip */ \"../../node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _cartesian_XAxis__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./cartesian/XAxis */ \"../../node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _cartesian_YAxis__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./cartesian/YAxis */ \"../../node_modules/recharts/es6/cartesian/YAxis.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_cartesian_Area__WEBPACK_IMPORTED_MODULE_0__, _chart_AreaChart__WEBPACK_IMPORTED_MODULE_1__, _cartesian_CartesianGrid__WEBPACK_IMPORTED_MODULE_2__, _component_Legend__WEBPACK_IMPORTED_MODULE_3__, _cartesian_Line__WEBPACK_IMPORTED_MODULE_4__, _component_ResponsiveContainer__WEBPACK_IMPORTED_MODULE_5__, _component_Tooltip__WEBPACK_IMPORTED_MODULE_6__, _cartesian_XAxis__WEBPACK_IMPORTED_MODULE_7__, _cartesian_YAxis__WEBPACK_IMPORTED_MODULE_8__]);\n([_cartesian_Area__WEBPACK_IMPORTED_MODULE_0__, _chart_AreaChart__WEBPACK_IMPORTED_MODULE_1__, _cartesian_CartesianGrid__WEBPACK_IMPORTED_MODULE_2__, _component_Legend__WEBPACK_IMPORTED_MODULE_3__, _cartesian_Line__WEBPACK_IMPORTED_MODULE_4__, _component_ResponsiveContainer__WEBPACK_IMPORTED_MODULE_5__, _component_Tooltip__WEBPACK_IMPORTED_MODULE_6__, _cartesian_XAxis__WEBPACK_IMPORTED_MODULE_7__, _cartesian_YAxis__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n// \"export type\" declarations on separate lines are in use\n// to workaround babel issue(s) 11465 12578\n//\n// see https://github.com/babel/babel/issues/11464#issuecomment-617606898\n\n\n\n\n\n\n\n\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BcmVhLEFyZWFDaGFydCxDYXJ0ZXNpYW5HcmlkLExlZ2VuZCxMaW5lLFJlc3BvbnNpdmVDb250YWluZXIsVG9vbHRpcCxYQXhpcyxZQXhpcyE9IS4uLy4uL25vZGVfbW9kdWxlcy9yZWNoYXJ0cy9lczYvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7O0FBRXVDO0FBQ007QUFDWTtBQUNkO0FBQ0o7QUFDOEI7QUFDeEI7QUFDSiIsInNvdXJjZXMiOlsid2VicGFjazovL3NvbGFuYS10cmFkaW5nLWRhc2hib2FyZC8uLi8uLi9ub2RlX21vZHVsZXMvcmVjaGFydHMvZXM2L2luZGV4LmpzPzM5NjciXSwic291cmNlc0NvbnRlbnQiOlsiLy8gXCJleHBvcnQgdHlwZVwiIGRlY2xhcmF0aW9ucyBvbiBzZXBhcmF0ZSBsaW5lcyBhcmUgaW4gdXNlXG4vLyB0byB3b3JrYXJvdW5kIGJhYmVsIGlzc3VlKHMpIDExNDY1IDEyNTc4XG4vL1xuLy8gc2VlIGh0dHBzOi8vZ2l0aHViLmNvbS9iYWJlbC9iYWJlbC9pc3N1ZXMvMTE0NjQjaXNzdWVjb21tZW50LTYxNzYwNjg5OFxuXG5leHBvcnQgeyBBcmVhIH0gZnJvbSBcIi4vY2FydGVzaWFuL0FyZWFcIlxuZXhwb3J0IHsgQXJlYUNoYXJ0IH0gZnJvbSBcIi4vY2hhcnQvQXJlYUNoYXJ0XCJcbmV4cG9ydCB7IENhcnRlc2lhbkdyaWQgfSBmcm9tIFwiLi9jYXJ0ZXNpYW4vQ2FydGVzaWFuR3JpZFwiXG5leHBvcnQgeyBMZWdlbmQgfSBmcm9tIFwiLi9jb21wb25lbnQvTGVnZW5kXCJcbmV4cG9ydCB7IExpbmUgfSBmcm9tIFwiLi9jYXJ0ZXNpYW4vTGluZVwiXG5leHBvcnQgeyBSZXNwb25zaXZlQ29udGFpbmVyIH0gZnJvbSBcIi4vY29tcG9uZW50L1Jlc3BvbnNpdmVDb250YWluZXJcIlxuZXhwb3J0IHsgVG9vbHRpcCB9IGZyb20gXCIuL2NvbXBvbmVudC9Ub29sdGlwXCJcbmV4cG9ydCB7IFhBeGlzIH0gZnJvbSBcIi4vY2FydGVzaWFuL1hBeGlzXCJcbmV4cG9ydCB7IFlBeGlzIH0gZnJvbSBcIi4vY2FydGVzaWFuL1lBeGlzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Area,AreaChart,CartesianGrid,Legend,Line,ResponsiveContainer,Tooltip,XAxis,YAxis!=!../../node_modules/recharts/es6/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=Box!=!../../node_modules/@mui/material/index.js":
/*!*********************************************************************************!*\
  !*** __barrel_optimize__?names=Box!=!../../node_modules/@mui/material/index.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Box: () => (/* reexport default from dynamic */ _Box__WEBPACK_IMPORTED_MODULE_0___default.a)\n/* harmony export */ });\n/* harmony import */ var _Box__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Box */ \"../../node_modules/@mui/material/node/Box/index.js\");\n/* harmony import */ var _Box__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Box__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * @mui/material v5.18.0\n *\n * @license MIT\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */ /* eslint-disable import/export */ \n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1Cb3ghPSEuLi8uLi9ub2RlX21vZHVsZXMvQG11aS9tYXRlcmlhbC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3NvbGFuYS10cmFkaW5nLWRhc2hib2FyZC8uLi8uLi9ub2RlX21vZHVsZXMvQG11aS9tYXRlcmlhbC9pbmRleC5qcz9iNTI3Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQG11aS9tYXRlcmlhbCB2NS4xOC4wXG4gKlxuICogQGxpY2Vuc2UgTUlUXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZSBmb3VuZCBpbiB0aGVcbiAqIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqLyAvKiBlc2xpbnQtZGlzYWJsZSBpbXBvcnQvZXhwb3J0ICovIFxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCb3ggfSBmcm9tIFwiLi9Cb3hcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Box!=!../../node_modules/@mui/material/index.js\n");

/***/ }),

/***/ "./components/TradingChart.tsx":
/*!*************************************!*\
  !*** ./components/TradingChart.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Legend_Line_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Legend,Line,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"__barrel_optimize__?names=Area,AreaChart,CartesianGrid,Legend,Line,ResponsiveContainer,Tooltip,XAxis,YAxis!=!../../node_modules/recharts/es6/index.js\");\n/* harmony import */ var _barrel_optimize_names_Box_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Box!=!@mui/material */ \"__barrel_optimize__?names=Box!=!../../node_modules/@mui/material/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_barrel_optimize_names_Area_AreaChart_CartesianGrid_Legend_Line_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__]);\n_barrel_optimize_names_Area_AreaChart_CartesianGrid_Legend_Line_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst TradingChart = ({ data })=>{\n    // Generate mock data if no data provided\n    const mockData = react__WEBPACK_IMPORTED_MODULE_1___default().useMemo(()=>{\n        if (data && data.length > 0) return data;\n        const now = new Date();\n        return Array.from({\n            length: 24\n        }, (_, i)=>{\n            const time = new Date(now.getTime() - (23 - i) * 60 * 60 * 1000);\n            return {\n                timestamp: time.toLocaleTimeString(\"en-US\", {\n                    hour: \"2-digit\",\n                    minute: \"2-digit\"\n                }),\n                volume: Math.random() * 1000000 + 500000,\n                price: Math.random() * 50 + 100,\n                trades: Math.floor(Math.random() * 500 + 100)\n            };\n        });\n    }, [\n        data\n    ]);\n    const formatCurrency = (value)=>{\n        return new Intl.NumberFormat(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\",\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 0\n        }).format(value);\n    };\n    const formatVolume = (value)=>{\n        if (value >= 1000000) {\n            return `$${(value / 1000000).toFixed(1)}M`;\n        }\n        if (value >= 1000) {\n            return `$${(value / 1000).toFixed(1)}K`;\n        }\n        return `$${value.toFixed(0)}`;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_mui_material__WEBPACK_IMPORTED_MODULE_2__.Box, {\n        sx: {\n            width: \"100%\",\n            height: 350\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Legend_Line_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__.ResponsiveContainer, {\n            width: \"100%\",\n            height: \"100%\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Legend_Line_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__.AreaChart, {\n                data: mockData,\n                margin: {\n                    top: 5,\n                    right: 30,\n                    left: 20,\n                    bottom: 5\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Legend_Line_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__.CartesianGrid, {\n                        strokeDasharray: \"3 3\",\n                        stroke: \"#f0f0f0\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\TradingChart.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Legend_Line_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__.XAxis, {\n                        dataKey: \"timestamp\",\n                        stroke: \"#666\",\n                        fontSize: 12\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\TradingChart.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Legend_Line_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__.YAxis, {\n                        yAxisId: \"volume\",\n                        orientation: \"left\",\n                        stroke: \"#8884d8\",\n                        fontSize: 12,\n                        tickFormatter: formatVolume\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\TradingChart.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Legend_Line_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__.YAxis, {\n                        yAxisId: \"price\",\n                        orientation: \"right\",\n                        stroke: \"#82ca9d\",\n                        fontSize: 12,\n                        tickFormatter: formatCurrency\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\TradingChart.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Legend_Line_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                        contentStyle: {\n                            backgroundColor: \"#fff\",\n                            border: \"1px solid #ccc\",\n                            borderRadius: \"8px\",\n                            boxShadow: \"0 4px 6px rgba(0, 0, 0, 0.1)\"\n                        },\n                        formatter: (value, name)=>{\n                            if (name === \"volume\") return [\n                                formatVolume(value),\n                                \"Volume\"\n                            ];\n                            if (name === \"price\") return [\n                                formatCurrency(value),\n                                \"Avg Price\"\n                            ];\n                            return [\n                                value,\n                                name\n                            ];\n                        }\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\TradingChart.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Legend_Line_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__.Legend, {}, void 0, false, {\n                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\TradingChart.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Legend_Line_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__.Area, {\n                        yAxisId: \"volume\",\n                        type: \"monotone\",\n                        dataKey: \"volume\",\n                        stroke: \"#8884d8\",\n                        fill: \"#8884d8\",\n                        fillOpacity: 0.3,\n                        name: \"Volume\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\TradingChart.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Legend_Line_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__.Line, {\n                        yAxisId: \"price\",\n                        type: \"monotone\",\n                        dataKey: \"price\",\n                        stroke: \"#82ca9d\",\n                        strokeWidth: 2,\n                        dot: false,\n                        name: \"Avg Price\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\TradingChart.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\TradingChart.tsx\",\n                lineNumber: 66,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\TradingChart.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\TradingChart.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TradingChart);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/TradingChart.tsx\n");

/***/ })

};
;