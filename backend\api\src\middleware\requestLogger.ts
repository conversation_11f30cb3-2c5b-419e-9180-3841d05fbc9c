import { Request, Response, NextFunction } from 'express';
import morgan from 'morgan';
import { config } from '../config/config';

// Custom token for response time in milliseconds
morgan.token('response-time-ms', (req: any, res: any) => {
  const responseTime = res.getHeader('X-Response-Time');
  return responseTime ? `${responseTime}ms` : '-';
});

// Custom token for request ID
morgan.token('request-id', (req: any) => {
  return req.id || '-';
});

// Custom token for user agent
morgan.token('user-agent', (req: any) => {
  return req.get('User-Agent') || '-';
});

// Custom token for real IP
morgan.token('real-ip', (req: any) => {
  return req.get('X-Real-IP') || req.get('X-Forwarded-For') || req.ip || '-';
});

// Development format
const devFormat = ':method :url :status :response-time ms - :res[content-length]';

// Production format with more details
const prodFormat = ':real-ip - :remote-user [:date[clf]] ":method :url HTTP/:http-version" :status :res[content-length] ":referrer" ":user-agent" :response-time-ms';

// JSON format for structured logging
const jsonFormat = (tokens: any, req: any, res: any) => {
  return JSON.stringify({
    timestamp: new Date().toISOString(),
    method: tokens.method(req, res),
    url: tokens.url(req, res),
    status: parseInt(tokens.status(req, res)),
    responseTime: parseFloat(tokens['response-time'](req, res)),
    contentLength: tokens.res(req, res, 'content-length'),
    userAgent: tokens['user-agent'](req, res),
    ip: tokens['real-ip'](req, res),
    referrer: tokens.referrer(req, res),
    requestId: tokens['request-id'](req, res),
  });
};

// Request ID middleware
export const addRequestId = (req: any, res: Response, next: NextFunction) => {
  req.id = Math.random().toString(36).substring(2, 11);
  res.setHeader('X-Request-ID', req.id);
  next();
};

// Response time middleware
export const addResponseTime = (req: Request, res: Response, next: NextFunction) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    res.setHeader('X-Response-Time', duration);
  });
  
  next();
};

// Main request logger
export const requestLogger = [
  addRequestId,
  addResponseTime,
  morgan(devFormat),
];
