"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "components_VolumeHeatmap_tsx";
exports.ids = ["components_VolumeHeatmap_tsx"];
exports.modules = {

/***/ "__barrel_optimize__?names=Box,Grid,Paper,Tooltip,Typography!=!../../node_modules/@mui/material/index.js":
/*!***************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Box,Grid,Paper,Tooltip,Typography!=!../../node_modules/@mui/material/index.js ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Box: () => (/* reexport default from dynamic */ _Box__WEBPACK_IMPORTED_MODULE_0___default.a),\n/* harmony export */   Grid: () => (/* reexport default from dynamic */ _Grid__WEBPACK_IMPORTED_MODULE_1___default.a),\n/* harmony export */   Paper: () => (/* reexport default from dynamic */ _Paper__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   Tooltip: () => (/* reexport default from dynamic */ _Tooltip__WEBPACK_IMPORTED_MODULE_3___default.a),\n/* harmony export */   Typography: () => (/* reexport default from dynamic */ _Typography__WEBPACK_IMPORTED_MODULE_4___default.a)\n/* harmony export */ });\n/* harmony import */ var _Box__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Box */ \"../../node_modules/@mui/material/node/Box/index.js\");\n/* harmony import */ var _Box__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Box__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Grid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Grid */ \"../../node_modules/@mui/material/node/Grid/index.js\");\n/* harmony import */ var _Grid__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_Grid__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Paper__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Paper */ \"../../node_modules/@mui/material/node/Paper/index.js\");\n/* harmony import */ var _Paper__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_Paper__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Tooltip__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Tooltip */ \"../../node_modules/@mui/material/node/Tooltip/index.js\");\n/* harmony import */ var _Tooltip__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_Tooltip__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _Typography__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Typography */ \"../../node_modules/@mui/material/node/Typography/index.js\");\n/* harmony import */ var _Typography__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_Typography__WEBPACK_IMPORTED_MODULE_4__);\n/**\n * @mui/material v5.18.0\n *\n * @license MIT\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */ /* eslint-disable import/export */ \n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1Cb3gsR3JpZCxQYXBlcixUb29sdGlwLFR5cG9ncmFwaHkhPSEuLi8uLi9ub2RlX21vZHVsZXMvQG11aS9tYXRlcmlhbC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNzQztBQUNFO0FBQ0U7QUFDSSIsInNvdXJjZXMiOlsid2VicGFjazovL3NvbGFuYS10cmFkaW5nLWRhc2hib2FyZC8uLi8uLi9ub2RlX21vZHVsZXMvQG11aS9tYXRlcmlhbC9pbmRleC5qcz8wMDRjIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQG11aS9tYXRlcmlhbCB2NS4xOC4wXG4gKlxuICogQGxpY2Vuc2UgTUlUXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZSBmb3VuZCBpbiB0aGVcbiAqIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqLyAvKiBlc2xpbnQtZGlzYWJsZSBpbXBvcnQvZXhwb3J0ICovIFxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCb3ggfSBmcm9tIFwiLi9Cb3hcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBHcmlkIH0gZnJvbSBcIi4vR3JpZFwiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFBhcGVyIH0gZnJvbSBcIi4vUGFwZXJcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBUb29sdGlwIH0gZnJvbSBcIi4vVG9vbHRpcFwiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFR5cG9ncmFwaHkgfSBmcm9tIFwiLi9UeXBvZ3JhcGh5XCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Box,Grid,Paper,Tooltip,Typography!=!../../node_modules/@mui/material/index.js\n");

/***/ }),

/***/ "./components/VolumeHeatmap.tsx":
/*!**************************************!*\
  !*** ./components/VolumeHeatmap.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_Grid_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Grid,Paper,Tooltip,Typography!=!@mui/material */ \"__barrel_optimize__?names=Box,Grid,Paper,Tooltip,Typography!=!../../node_modules/@mui/material/index.js\");\n\n\n\nconst VolumeHeatmap = ()=>{\n    // Mock data for demonstration\n    const mockTokens = [\n        {\n            symbol: \"SOL\",\n            volume: 125000000,\n            change24h: 12.5,\n            trades: 45000\n        },\n        {\n            symbol: \"USDC\",\n            volume: 89000000,\n            change24h: 0.1,\n            trades: 32000\n        },\n        {\n            symbol: \"BONK\",\n            volume: 67000000,\n            change24h: -8.3,\n            trades: 28000\n        },\n        {\n            symbol: \"WIF\",\n            volume: 45000000,\n            change24h: 15.7,\n            trades: 18000\n        },\n        {\n            symbol: \"JUP\",\n            volume: 34000000,\n            change24h: 5.2,\n            trades: 15000\n        },\n        {\n            symbol: \"PYTH\",\n            volume: 28000000,\n            change24h: -3.1,\n            trades: 12000\n        },\n        {\n            symbol: \"RAY\",\n            volume: 23000000,\n            change24h: 8.9,\n            trades: 11000\n        },\n        {\n            symbol: \"ORCA\",\n            volume: 18000000,\n            change24h: -1.5,\n            trades: 8500\n        },\n        {\n            symbol: \"SRM\",\n            volume: 15000000,\n            change24h: 22.1,\n            trades: 7200\n        },\n        {\n            symbol: \"MNGO\",\n            volume: 12000000,\n            change24h: -12.4,\n            trades: 6800\n        },\n        {\n            symbol: \"STEP\",\n            volume: 9000000,\n            change24h: 6.7,\n            trades: 5400\n        },\n        {\n            symbol: \"COPE\",\n            volume: 7500000,\n            change24h: -5.8,\n            trades: 4200\n        }\n    ];\n    const getVolumeIntensity = (volume, maxVolume)=>{\n        return Math.min(volume / maxVolume, 1);\n    };\n    const getChangeColor = (change)=>{\n        if (change > 10) return \"#4caf50\"; // Strong green\n        if (change > 0) return \"#8bc34a\"; // Light green\n        if (change > -5) return \"#ff9800\"; // Orange\n        return \"#f44336\"; // Red\n    };\n    const formatVolume = (volume)=>{\n        if (volume >= 1000000) {\n            return `$${(volume / 1000000).toFixed(1)}M`;\n        }\n        if (volume >= 1000) {\n            return `$${(volume / 1000).toFixed(1)}K`;\n        }\n        return `$${volume.toFixed(0)}`;\n    };\n    const maxVolume = Math.max(...mockTokens.map((t)=>t.volume));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__.Box, {\n        sx: {\n            height: 400,\n            overflow: \"auto\",\n            p: 1\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {\n                container: true,\n                spacing: 1,\n                children: mockTokens.map((token)=>{\n                    const intensity = getVolumeIntensity(token.volume, maxVolume);\n                    const backgroundColor = getChangeColor(token.change24h);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {\n                        item: true,\n                        xs: 6,\n                        sm: 4,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {\n                            title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__.Box, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                        variant: \"subtitle2\",\n                                        children: token.symbol\n                                    }, void 0, false, void 0, void 0),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                        variant: \"body2\",\n                                        children: [\n                                            \"Volume: \",\n                                            formatVolume(token.volume)\n                                        ]\n                                    }, void 0, true, void 0, void 0),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                        variant: \"body2\",\n                                        children: [\n                                            \"24h Change: \",\n                                            token.change24h > 0 ? \"+\" : \"\",\n                                            token.change24h.toFixed(1),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, void 0, void 0),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                        variant: \"body2\",\n                                        children: [\n                                            \"Trades: \",\n                                            token.trades.toLocaleString()\n                                        ]\n                                    }, void 0, true, void 0, void 0)\n                                ]\n                            }, void 0, true, void 0, void 0),\n                            arrow: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__.Paper, {\n                                elevation: 2,\n                                sx: {\n                                    p: 1.5,\n                                    height: 80,\n                                    display: \"flex\",\n                                    flexDirection: \"column\",\n                                    justifyContent: \"center\",\n                                    alignItems: \"center\",\n                                    backgroundColor,\n                                    opacity: 0.3 + intensity * 0.7,\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease-in-out\",\n                                    \"&:hover\": {\n                                        opacity: 1,\n                                        transform: \"scale(1.05)\",\n                                        zIndex: 1\n                                    }\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                        variant: \"subtitle2\",\n                                        fontWeight: \"bold\",\n                                        color: \"white\",\n                                        textAlign: \"center\",\n                                        children: token.symbol\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\VolumeHeatmap.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                        variant: \"caption\",\n                                        color: \"white\",\n                                        textAlign: \"center\",\n                                        children: formatVolume(token.volume)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\VolumeHeatmap.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                        variant: \"caption\",\n                                        color: \"white\",\n                                        textAlign: \"center\",\n                                        fontWeight: \"bold\",\n                                        children: [\n                                            token.change24h > 0 ? \"+\" : \"\",\n                                            token.change24h.toFixed(1),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\VolumeHeatmap.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\VolumeHeatmap.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\VolumeHeatmap.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 15\n                        }, undefined)\n                    }, token.symbol, false, {\n                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\VolumeHeatmap.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 13\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\VolumeHeatmap.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__.Box, {\n                mt: 2,\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                gap: 2,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"Color: 24h Change\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\VolumeHeatmap.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__.Box, {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        gap: 1,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__.Box, {\n                                sx: {\n                                    width: 12,\n                                    height: 12,\n                                    backgroundColor: \"#f44336\",\n                                    borderRadius: 1\n                                }\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\VolumeHeatmap.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                variant: \"caption\",\n                                color: \"text.secondary\",\n                                children: \"-10%\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\VolumeHeatmap.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\VolumeHeatmap.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__.Box, {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        gap: 1,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__.Box, {\n                                sx: {\n                                    width: 12,\n                                    height: 12,\n                                    backgroundColor: \"#ff9800\",\n                                    borderRadius: 1\n                                }\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\VolumeHeatmap.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                variant: \"caption\",\n                                color: \"text.secondary\",\n                                children: \"0%\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\VolumeHeatmap.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\VolumeHeatmap.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__.Box, {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        gap: 1,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__.Box, {\n                                sx: {\n                                    width: 12,\n                                    height: 12,\n                                    backgroundColor: \"#4caf50\",\n                                    borderRadius: 1\n                                }\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\VolumeHeatmap.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                variant: \"caption\",\n                                color: \"text.secondary\",\n                                children: \"+10%\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\VolumeHeatmap.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\VolumeHeatmap.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Paper_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        ml: 2,\n                        children: \"Opacity: Volume\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\VolumeHeatmap.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\VolumeHeatmap.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\VolumeHeatmap.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (VolumeHeatmap);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/VolumeHeatmap.tsx\n");

/***/ })

};
;