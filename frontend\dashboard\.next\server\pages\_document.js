"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_document";
exports.ids = ["pages/_document"];
exports.modules = {

/***/ "./pages/_document.tsx":
/*!*****************************!*\
  !*** ./pages/_document.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MyDocument)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/document */ \"../../node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/createEmotionCache */ \"./utils/createEmotionCache.ts\");\n/* harmony import */ var _emotion_server_create_instance__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @emotion/server/create-instance */ \"@emotion/server/create-instance\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_3__, _emotion_server_create_instance__WEBPACK_IMPORTED_MODULE_4__]);\n([_utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_3__, _emotion_server_create_instance__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nclass MyDocument extends (next_document__WEBPACK_IMPORTED_MODULE_2___default()) {\n    static async getInitialProps(ctx) {\n        const originalRenderPage = ctx.renderPage;\n        const cache = (0,_utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_3__.createEmotionCache)();\n        const { extractCriticalToChunks } = (0,_emotion_server_create_instance__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(cache);\n        ctx.renderPage = ()=>originalRenderPage({\n                enhanceApp: (App)=>function EnhanceApp(props) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(App, {\n                            emotionCache: cache,\n                            ...props\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 20\n                        }, this);\n                    }\n            });\n        const initialProps = await next_document__WEBPACK_IMPORTED_MODULE_2___default().getInitialProps(ctx);\n        const emotionStyles = extractCriticalToChunks(initialProps.html);\n        const emotionStyleTags = emotionStyles.styles.map((style)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                \"data-emotion\": `${style.key} ${style.ids.join(\" \")}`,\n                dangerouslySetInnerHTML: {\n                    __html: style.css\n                }\n            }, style.key, false, {\n                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this));\n        return {\n            ...initialProps,\n            emotionStyleTags\n        };\n    }\n    render() {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.Html, {\n            lang: \"en\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.Head, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            charSet: \"utf-8\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"theme-color\",\n                            content: \"#1976d2\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"description\",\n                            content: \"Real-time Solana trading intelligence dashboard with whale tracking, rugpull detection, and advanced analytics\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"keywords\",\n                            content: \"solana, trading, intelligence, whale tracking, rugpull detection, crypto analytics, defi, blockchain\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"author\",\n                            content: \"HectorTa1989\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"robots\",\n                            content: \"index, follow\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            property: \"og:type\",\n                            content: \"website\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            property: \"og:title\",\n                            content: \"Solana Trading Intelligence System\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            property: \"og:description\",\n                            content: \"Real-time Solana trading intelligence dashboard with whale tracking, rugpull detection, and advanced analytics\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            property: \"og:image\",\n                            content: \"/og-image.png\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            property: \"og:url\",\n                            content: \"https://solana-intel.io\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            property: \"og:site_name\",\n                            content: \"Solana Trading Intelligence\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"twitter:card\",\n                            content: \"summary_large_image\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"twitter:title\",\n                            content: \"Solana Trading Intelligence System\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"twitter:description\",\n                            content: \"Real-time Solana trading intelligence dashboard with whale tracking, rugpull detection, and advanced analytics\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"twitter:image\",\n                            content: \"/twitter-image.png\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"twitter:creator\",\n                            content: \"@HectorTa1989\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            href: \"/favicon.ico\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            type: \"image/png\",\n                            sizes: \"32x32\",\n                            href: \"/favicon-32x32.png\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            type: \"image/png\",\n                            sizes: \"16x16\",\n                            href: \"/favicon-16x16.png\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"apple-touch-icon\",\n                            href: \"/apple-touch-icon.png\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"manifest\",\n                            href: \"/manifest.json\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"preconnect\",\n                            href: \"https://fonts.googleapis.com\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"preconnect\",\n                            href: \"https://fonts.gstatic.com\",\n                            crossOrigin: \"anonymous\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"stylesheet\",\n                            href: \"https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700&display=swap\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"stylesheet\",\n                            href: \"https://fonts.googleapis.com/icon?family=Material+Icons&display=swap\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                            type: \"application/ld+json\",\n                            dangerouslySetInnerHTML: {\n                                __html: JSON.stringify({\n                                    \"@context\": \"https://schema.org\",\n                                    \"@type\": \"WebApplication\",\n                                    \"name\": \"Solana Trading Intelligence System\",\n                                    \"description\": \"Real-time Solana trading intelligence dashboard with whale tracking, rugpull detection, and advanced analytics\",\n                                    \"url\": \"https://solana-intel.io\",\n                                    \"applicationCategory\": \"FinanceApplication\",\n                                    \"operatingSystem\": \"Web\",\n                                    \"author\": {\n                                        \"@type\": \"Person\",\n                                        \"name\": \"HectorTa1989\",\n                                        \"url\": \"https://github.com/HectorTa1989\"\n                                    },\n                                    \"offers\": {\n                                        \"@type\": \"Offer\",\n                                        \"price\": \"0\",\n                                        \"priceCurrency\": \"USD\"\n                                    }\n                                })\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"dns-prefetch\",\n                            href: \"//api.solana-intel.io\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"preconnect\",\n                            href: \"https://api.solana-intel.io\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this),\n                        this.props.emotionStyleTags\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.Main, {}, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.NextScript, {}, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\pages\\\\_document.tsx\",\n            lineNumber: 38,\n            columnNumber: 7\n        }, this);\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fZG9jdW1lbnQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBMEI7QUFDOEQ7QUFDdkI7QUFDQztBQUVuRCxNQUFNUSxtQkFBbUJQLHNEQUFRQTtJQUM5QyxhQUFhUSxnQkFBZ0JDLEdBQW9CLEVBQUU7UUFDakQsTUFBTUMscUJBQXFCRCxJQUFJRSxVQUFVO1FBQ3pDLE1BQU1DLFFBQVFQLDZFQUFrQkE7UUFDaEMsTUFBTSxFQUFFUSx1QkFBdUIsRUFBRSxHQUFHUCwyRUFBbUJBLENBQUNNO1FBRXhESCxJQUFJRSxVQUFVLEdBQUcsSUFDZkQsbUJBQW1CO2dCQUNqQkksWUFBWSxDQUFDQyxNQUNYLFNBQVNDLFdBQVdDLEtBQUs7d0JBQ3ZCLHFCQUFPLDhEQUFDRjs0QkFBSUcsY0FBY047NEJBQVEsR0FBR0ssS0FBSzs7Ozs7O29CQUM1QztZQUNKO1FBRUYsTUFBTUUsZUFBZSxNQUFNbkIsb0VBQXdCLENBQUNTO1FBQ3BELE1BQU1XLGdCQUFnQlAsd0JBQXdCTSxhQUFhRSxJQUFJO1FBQy9ELE1BQU1DLG1CQUFtQkYsY0FBY0csTUFBTSxDQUFDQyxHQUFHLENBQUMsQ0FBQ0Msc0JBQ2pELDhEQUFDQTtnQkFDQ0MsZ0JBQWMsQ0FBQyxFQUFFRCxNQUFNRSxHQUFHLENBQUMsQ0FBQyxFQUFFRixNQUFNRyxHQUFHLENBQUNDLElBQUksQ0FBQyxLQUFLLENBQUM7Z0JBRW5EQyx5QkFBeUI7b0JBQUVDLFFBQVFOLE1BQU1PLEdBQUc7Z0JBQUM7ZUFEeENQLE1BQU1FLEdBQUc7Ozs7O1FBS2xCLE9BQU87WUFDTCxHQUFHUixZQUFZO1lBQ2ZHO1FBQ0Y7SUFDRjtJQUVBVyxTQUFTO1FBQ1AscUJBQ0UsOERBQUNoQywrQ0FBSUE7WUFBQ2lDLE1BQUs7OzhCQUNULDhEQUFDaEMsK0NBQUlBOztzQ0FFSCw4REFBQ2lDOzRCQUFLQyxTQUFROzs7Ozs7c0NBQ2QsOERBQUNEOzRCQUFLRSxNQUFLOzRCQUFjQyxTQUFROzs7Ozs7c0NBQ2pDLDhEQUFDSDs0QkFBS0UsTUFBSzs0QkFBY0MsU0FBUTs7Ozs7O3NDQUNqQyw4REFBQ0g7NEJBQUtFLE1BQUs7NEJBQVdDLFNBQVE7Ozs7OztzQ0FDOUIsOERBQUNIOzRCQUFLRSxNQUFLOzRCQUFTQyxTQUFROzs7Ozs7c0NBQzVCLDhEQUFDSDs0QkFBS0UsTUFBSzs0QkFBU0MsU0FBUTs7Ozs7O3NDQUc1Qiw4REFBQ0g7NEJBQUtJLFVBQVM7NEJBQVVELFNBQVE7Ozs7OztzQ0FDakMsOERBQUNIOzRCQUFLSSxVQUFTOzRCQUFXRCxTQUFROzs7Ozs7c0NBQ2xDLDhEQUFDSDs0QkFBS0ksVUFBUzs0QkFBaUJELFNBQVE7Ozs7OztzQ0FDeEMsOERBQUNIOzRCQUFLSSxVQUFTOzRCQUFXRCxTQUFROzs7Ozs7c0NBQ2xDLDhEQUFDSDs0QkFBS0ksVUFBUzs0QkFBU0QsU0FBUTs7Ozs7O3NDQUNoQyw4REFBQ0g7NEJBQUtJLFVBQVM7NEJBQWVELFNBQVE7Ozs7OztzQ0FHdEMsOERBQUNIOzRCQUFLRSxNQUFLOzRCQUFlQyxTQUFROzs7Ozs7c0NBQ2xDLDhEQUFDSDs0QkFBS0UsTUFBSzs0QkFBZ0JDLFNBQVE7Ozs7OztzQ0FDbkMsOERBQUNIOzRCQUFLRSxNQUFLOzRCQUFzQkMsU0FBUTs7Ozs7O3NDQUN6Qyw4REFBQ0g7NEJBQUtFLE1BQUs7NEJBQWdCQyxTQUFROzs7Ozs7c0NBQ25DLDhEQUFDSDs0QkFBS0UsTUFBSzs0QkFBa0JDLFNBQVE7Ozs7OztzQ0FHckMsOERBQUNFOzRCQUFLQyxLQUFJOzRCQUFPQyxNQUFLOzs7Ozs7c0NBQ3RCLDhEQUFDRjs0QkFBS0MsS0FBSTs0QkFBT0UsTUFBSzs0QkFBWUMsT0FBTTs0QkFBUUYsTUFBSzs7Ozs7O3NDQUNyRCw4REFBQ0Y7NEJBQUtDLEtBQUk7NEJBQU9FLE1BQUs7NEJBQVlDLE9BQU07NEJBQVFGLE1BQUs7Ozs7OztzQ0FDckQsOERBQUNGOzRCQUFLQyxLQUFJOzRCQUFtQkMsTUFBSzs7Ozs7O3NDQUNsQyw4REFBQ0Y7NEJBQUtDLEtBQUk7NEJBQVdDLE1BQUs7Ozs7OztzQ0FHMUIsOERBQUNGOzRCQUFLQyxLQUFJOzRCQUFhQyxNQUFLOzs7Ozs7c0NBQzVCLDhEQUFDRjs0QkFBS0MsS0FBSTs0QkFBYUMsTUFBSzs0QkFBNEJHLGFBQVk7Ozs7OztzQ0FDcEUsOERBQUNMOzRCQUNDQyxLQUFJOzRCQUNKQyxNQUFLOzs7Ozs7c0NBRVAsOERBQUNGOzRCQUNDQyxLQUFJOzRCQUNKQyxNQUFLOzs7Ozs7c0NBSVAsOERBQUNJOzRCQUNDSCxNQUFLOzRCQUNMYix5QkFBeUI7Z0NBQ3ZCQyxRQUFRZ0IsS0FBS0MsU0FBUyxDQUFDO29DQUNyQixZQUFZO29DQUNaLFNBQVM7b0NBQ1QsUUFBUTtvQ0FDUixlQUFlO29DQUNmLE9BQU87b0NBQ1AsdUJBQXVCO29DQUN2QixtQkFBbUI7b0NBQ25CLFVBQVU7d0NBQ1IsU0FBUzt3Q0FDVCxRQUFRO3dDQUNSLE9BQU87b0NBQ1Q7b0NBQ0EsVUFBVTt3Q0FDUixTQUFTO3dDQUNULFNBQVM7d0NBQ1QsaUJBQWlCO29DQUNuQjtnQ0FDRjs0QkFDRjs7Ozs7O3NDQUlGLDhEQUFDUjs0QkFBS0MsS0FBSTs0QkFBZUMsTUFBSzs7Ozs7O3NDQUM5Qiw4REFBQ0Y7NEJBQUtDLEtBQUk7NEJBQWFDLE1BQUs7Ozs7Ozt3QkFHMUIsSUFBSSxDQUFDekIsS0FBSyxDQUFTSyxnQkFBZ0I7Ozs7Ozs7OEJBRXZDLDhEQUFDMkI7O3NDQUNDLDhEQUFDOUMsK0NBQUlBOzs7OztzQ0FDTCw4REFBQ0MscURBQVVBOzs7Ozs7Ozs7Ozs7Ozs7OztJQUluQjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc29sYW5hLXRyYWRpbmctZGFzaGJvYXJkLy4vcGFnZXMvX2RvY3VtZW50LnRzeD9kMzdkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgRG9jdW1lbnQsIHsgSHRtbCwgSGVhZCwgTWFpbiwgTmV4dFNjcmlwdCwgRG9jdW1lbnRDb250ZXh0IH0gZnJvbSAnbmV4dC9kb2N1bWVudCc7XG5pbXBvcnQgeyBjcmVhdGVFbW90aW9uQ2FjaGUgfSBmcm9tICcuLi91dGlscy9jcmVhdGVFbW90aW9uQ2FjaGUnO1xuaW1wb3J0IGNyZWF0ZUVtb3Rpb25TZXJ2ZXIgZnJvbSAnQGVtb3Rpb24vc2VydmVyL2NyZWF0ZS1pbnN0YW5jZSc7XG5cbmV4cG9ydCBkZWZhdWx0IGNsYXNzIE15RG9jdW1lbnQgZXh0ZW5kcyBEb2N1bWVudCB7XG4gIHN0YXRpYyBhc3luYyBnZXRJbml0aWFsUHJvcHMoY3R4OiBEb2N1bWVudENvbnRleHQpIHtcbiAgICBjb25zdCBvcmlnaW5hbFJlbmRlclBhZ2UgPSBjdHgucmVuZGVyUGFnZTtcbiAgICBjb25zdCBjYWNoZSA9IGNyZWF0ZUVtb3Rpb25DYWNoZSgpO1xuICAgIGNvbnN0IHsgZXh0cmFjdENyaXRpY2FsVG9DaHVua3MgfSA9IGNyZWF0ZUVtb3Rpb25TZXJ2ZXIoY2FjaGUpO1xuXG4gICAgY3R4LnJlbmRlclBhZ2UgPSAoKSA9PlxuICAgICAgb3JpZ2luYWxSZW5kZXJQYWdlKHtcbiAgICAgICAgZW5oYW5jZUFwcDogKEFwcDogYW55KSA9PlxuICAgICAgICAgIGZ1bmN0aW9uIEVuaGFuY2VBcHAocHJvcHMpIHtcbiAgICAgICAgICAgIHJldHVybiA8QXBwIGVtb3Rpb25DYWNoZT17Y2FjaGV9IHsuLi5wcm9wc30gLz47XG4gICAgICAgICAgfSxcbiAgICAgIH0pO1xuXG4gICAgY29uc3QgaW5pdGlhbFByb3BzID0gYXdhaXQgRG9jdW1lbnQuZ2V0SW5pdGlhbFByb3BzKGN0eCk7XG4gICAgY29uc3QgZW1vdGlvblN0eWxlcyA9IGV4dHJhY3RDcml0aWNhbFRvQ2h1bmtzKGluaXRpYWxQcm9wcy5odG1sKTtcbiAgICBjb25zdCBlbW90aW9uU3R5bGVUYWdzID0gZW1vdGlvblN0eWxlcy5zdHlsZXMubWFwKChzdHlsZSkgPT4gKFxuICAgICAgPHN0eWxlXG4gICAgICAgIGRhdGEtZW1vdGlvbj17YCR7c3R5bGUua2V5fSAke3N0eWxlLmlkcy5qb2luKCcgJyl9YH1cbiAgICAgICAga2V5PXtzdHlsZS5rZXl9XG4gICAgICAgIGRhbmdlcm91c2x5U2V0SW5uZXJIVE1MPXt7IF9faHRtbDogc3R5bGUuY3NzIH19XG4gICAgICAvPlxuICAgICkpO1xuXG4gICAgcmV0dXJuIHtcbiAgICAgIC4uLmluaXRpYWxQcm9wcyxcbiAgICAgIGVtb3Rpb25TdHlsZVRhZ3MsXG4gICAgfTtcbiAgfVxuXG4gIHJlbmRlcigpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPEh0bWwgbGFuZz1cImVuXCI+XG4gICAgICAgIDxIZWFkPlxuICAgICAgICAgIHsvKiBTRU8gTWV0YSBUYWdzICovfVxuICAgICAgICAgIDxtZXRhIGNoYXJTZXQ9XCJ1dGYtOFwiIC8+XG4gICAgICAgICAgPG1ldGEgbmFtZT1cInRoZW1lLWNvbG9yXCIgY29udGVudD1cIiMxOTc2ZDJcIiAvPlxuICAgICAgICAgIDxtZXRhIG5hbWU9XCJkZXNjcmlwdGlvblwiIGNvbnRlbnQ9XCJSZWFsLXRpbWUgU29sYW5hIHRyYWRpbmcgaW50ZWxsaWdlbmNlIGRhc2hib2FyZCB3aXRoIHdoYWxlIHRyYWNraW5nLCBydWdwdWxsIGRldGVjdGlvbiwgYW5kIGFkdmFuY2VkIGFuYWx5dGljc1wiIC8+XG4gICAgICAgICAgPG1ldGEgbmFtZT1cImtleXdvcmRzXCIgY29udGVudD1cInNvbGFuYSwgdHJhZGluZywgaW50ZWxsaWdlbmNlLCB3aGFsZSB0cmFja2luZywgcnVncHVsbCBkZXRlY3Rpb24sIGNyeXB0byBhbmFseXRpY3MsIGRlZmksIGJsb2NrY2hhaW5cIiAvPlxuICAgICAgICAgIDxtZXRhIG5hbWU9XCJhdXRob3JcIiBjb250ZW50PVwiSGVjdG9yVGExOTg5XCIgLz5cbiAgICAgICAgICA8bWV0YSBuYW1lPVwicm9ib3RzXCIgY29udGVudD1cImluZGV4LCBmb2xsb3dcIiAvPlxuXG4gICAgICAgICAgey8qIE9wZW4gR3JhcGggLyBGYWNlYm9vayAqL31cbiAgICAgICAgICA8bWV0YSBwcm9wZXJ0eT1cIm9nOnR5cGVcIiBjb250ZW50PVwid2Vic2l0ZVwiIC8+XG4gICAgICAgICAgPG1ldGEgcHJvcGVydHk9XCJvZzp0aXRsZVwiIGNvbnRlbnQ9XCJTb2xhbmEgVHJhZGluZyBJbnRlbGxpZ2VuY2UgU3lzdGVtXCIgLz5cbiAgICAgICAgICA8bWV0YSBwcm9wZXJ0eT1cIm9nOmRlc2NyaXB0aW9uXCIgY29udGVudD1cIlJlYWwtdGltZSBTb2xhbmEgdHJhZGluZyBpbnRlbGxpZ2VuY2UgZGFzaGJvYXJkIHdpdGggd2hhbGUgdHJhY2tpbmcsIHJ1Z3B1bGwgZGV0ZWN0aW9uLCBhbmQgYWR2YW5jZWQgYW5hbHl0aWNzXCIgLz5cbiAgICAgICAgICA8bWV0YSBwcm9wZXJ0eT1cIm9nOmltYWdlXCIgY29udGVudD1cIi9vZy1pbWFnZS5wbmdcIiAvPlxuICAgICAgICAgIDxtZXRhIHByb3BlcnR5PVwib2c6dXJsXCIgY29udGVudD1cImh0dHBzOi8vc29sYW5hLWludGVsLmlvXCIgLz5cbiAgICAgICAgICA8bWV0YSBwcm9wZXJ0eT1cIm9nOnNpdGVfbmFtZVwiIGNvbnRlbnQ9XCJTb2xhbmEgVHJhZGluZyBJbnRlbGxpZ2VuY2VcIiAvPlxuXG4gICAgICAgICAgey8qIFR3aXR0ZXIgKi99XG4gICAgICAgICAgPG1ldGEgbmFtZT1cInR3aXR0ZXI6Y2FyZFwiIGNvbnRlbnQ9XCJzdW1tYXJ5X2xhcmdlX2ltYWdlXCIgLz5cbiAgICAgICAgICA8bWV0YSBuYW1lPVwidHdpdHRlcjp0aXRsZVwiIGNvbnRlbnQ9XCJTb2xhbmEgVHJhZGluZyBJbnRlbGxpZ2VuY2UgU3lzdGVtXCIgLz5cbiAgICAgICAgICA8bWV0YSBuYW1lPVwidHdpdHRlcjpkZXNjcmlwdGlvblwiIGNvbnRlbnQ9XCJSZWFsLXRpbWUgU29sYW5hIHRyYWRpbmcgaW50ZWxsaWdlbmNlIGRhc2hib2FyZCB3aXRoIHdoYWxlIHRyYWNraW5nLCBydWdwdWxsIGRldGVjdGlvbiwgYW5kIGFkdmFuY2VkIGFuYWx5dGljc1wiIC8+XG4gICAgICAgICAgPG1ldGEgbmFtZT1cInR3aXR0ZXI6aW1hZ2VcIiBjb250ZW50PVwiL3R3aXR0ZXItaW1hZ2UucG5nXCIgLz5cbiAgICAgICAgICA8bWV0YSBuYW1lPVwidHdpdHRlcjpjcmVhdG9yXCIgY29udGVudD1cIkBIZWN0b3JUYTE5ODlcIiAvPlxuXG4gICAgICAgICAgey8qIEZhdmljb24gKi99XG4gICAgICAgICAgPGxpbmsgcmVsPVwiaWNvblwiIGhyZWY9XCIvZmF2aWNvbi5pY29cIiAvPlxuICAgICAgICAgIDxsaW5rIHJlbD1cImljb25cIiB0eXBlPVwiaW1hZ2UvcG5nXCIgc2l6ZXM9XCIzMngzMlwiIGhyZWY9XCIvZmF2aWNvbi0zMngzMi5wbmdcIiAvPlxuICAgICAgICAgIDxsaW5rIHJlbD1cImljb25cIiB0eXBlPVwiaW1hZ2UvcG5nXCIgc2l6ZXM9XCIxNngxNlwiIGhyZWY9XCIvZmF2aWNvbi0xNngxNi5wbmdcIiAvPlxuICAgICAgICAgIDxsaW5rIHJlbD1cImFwcGxlLXRvdWNoLWljb25cIiBocmVmPVwiL2FwcGxlLXRvdWNoLWljb24ucG5nXCIgLz5cbiAgICAgICAgICA8bGluayByZWw9XCJtYW5pZmVzdFwiIGhyZWY9XCIvbWFuaWZlc3QuanNvblwiIC8+XG5cbiAgICAgICAgICB7LyogRm9udHMgKi99XG4gICAgICAgICAgPGxpbmsgcmVsPVwicHJlY29ubmVjdFwiIGhyZWY9XCJodHRwczovL2ZvbnRzLmdvb2dsZWFwaXMuY29tXCIgLz5cbiAgICAgICAgICA8bGluayByZWw9XCJwcmVjb25uZWN0XCIgaHJlZj1cImh0dHBzOi8vZm9udHMuZ3N0YXRpYy5jb21cIiBjcm9zc09yaWdpbj1cImFub255bW91c1wiIC8+XG4gICAgICAgICAgPGxpbmtcbiAgICAgICAgICAgIHJlbD1cInN0eWxlc2hlZXRcIlxuICAgICAgICAgICAgaHJlZj1cImh0dHBzOi8vZm9udHMuZ29vZ2xlYXBpcy5jb20vY3NzMj9mYW1pbHk9Um9ib3RvOndnaHRAMzAwOzQwMDs1MDA7NjAwOzcwMCZkaXNwbGF5PXN3YXBcIlxuICAgICAgICAgIC8+XG4gICAgICAgICAgPGxpbmtcbiAgICAgICAgICAgIHJlbD1cInN0eWxlc2hlZXRcIlxuICAgICAgICAgICAgaHJlZj1cImh0dHBzOi8vZm9udHMuZ29vZ2xlYXBpcy5jb20vaWNvbj9mYW1pbHk9TWF0ZXJpYWwrSWNvbnMmZGlzcGxheT1zd2FwXCJcbiAgICAgICAgICAvPlxuXG4gICAgICAgICAgey8qIFN0cnVjdHVyZWQgRGF0YSAqL31cbiAgICAgICAgICA8c2NyaXB0XG4gICAgICAgICAgICB0eXBlPVwiYXBwbGljYXRpb24vbGQranNvblwiXG4gICAgICAgICAgICBkYW5nZXJvdXNseVNldElubmVySFRNTD17e1xuICAgICAgICAgICAgICBfX2h0bWw6IEpTT04uc3RyaW5naWZ5KHtcbiAgICAgICAgICAgICAgICBcIkBjb250ZXh0XCI6IFwiaHR0cHM6Ly9zY2hlbWEub3JnXCIsXG4gICAgICAgICAgICAgICAgXCJAdHlwZVwiOiBcIldlYkFwcGxpY2F0aW9uXCIsXG4gICAgICAgICAgICAgICAgXCJuYW1lXCI6IFwiU29sYW5hIFRyYWRpbmcgSW50ZWxsaWdlbmNlIFN5c3RlbVwiLFxuICAgICAgICAgICAgICAgIFwiZGVzY3JpcHRpb25cIjogXCJSZWFsLXRpbWUgU29sYW5hIHRyYWRpbmcgaW50ZWxsaWdlbmNlIGRhc2hib2FyZCB3aXRoIHdoYWxlIHRyYWNraW5nLCBydWdwdWxsIGRldGVjdGlvbiwgYW5kIGFkdmFuY2VkIGFuYWx5dGljc1wiLFxuICAgICAgICAgICAgICAgIFwidXJsXCI6IFwiaHR0cHM6Ly9zb2xhbmEtaW50ZWwuaW9cIixcbiAgICAgICAgICAgICAgICBcImFwcGxpY2F0aW9uQ2F0ZWdvcnlcIjogXCJGaW5hbmNlQXBwbGljYXRpb25cIixcbiAgICAgICAgICAgICAgICBcIm9wZXJhdGluZ1N5c3RlbVwiOiBcIldlYlwiLFxuICAgICAgICAgICAgICAgIFwiYXV0aG9yXCI6IHtcbiAgICAgICAgICAgICAgICAgIFwiQHR5cGVcIjogXCJQZXJzb25cIixcbiAgICAgICAgICAgICAgICAgIFwibmFtZVwiOiBcIkhlY3RvclRhMTk4OVwiLFxuICAgICAgICAgICAgICAgICAgXCJ1cmxcIjogXCJodHRwczovL2dpdGh1Yi5jb20vSGVjdG9yVGExOTg5XCJcbiAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgIFwib2ZmZXJzXCI6IHtcbiAgICAgICAgICAgICAgICAgIFwiQHR5cGVcIjogXCJPZmZlclwiLFxuICAgICAgICAgICAgICAgICAgXCJwcmljZVwiOiBcIjBcIixcbiAgICAgICAgICAgICAgICAgIFwicHJpY2VDdXJyZW5jeVwiOiBcIlVTRFwiXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgfX1cbiAgICAgICAgICAvPlxuXG4gICAgICAgICAgey8qIFBlcmZvcm1hbmNlIGFuZCBTZWN1cml0eSAqL31cbiAgICAgICAgICA8bGluayByZWw9XCJkbnMtcHJlZmV0Y2hcIiBocmVmPVwiLy9hcGkuc29sYW5hLWludGVsLmlvXCIgLz5cbiAgICAgICAgICA8bGluayByZWw9XCJwcmVjb25uZWN0XCIgaHJlZj1cImh0dHBzOi8vYXBpLnNvbGFuYS1pbnRlbC5pb1wiIC8+XG5cbiAgICAgICAgICB7LyogRW1vdGlvbiBTdHlsZXMgKi99XG4gICAgICAgICAgeyh0aGlzLnByb3BzIGFzIGFueSkuZW1vdGlvblN0eWxlVGFnc31cbiAgICAgICAgPC9IZWFkPlxuICAgICAgICA8Ym9keT5cbiAgICAgICAgICA8TWFpbiAvPlxuICAgICAgICAgIDxOZXh0U2NyaXB0IC8+XG4gICAgICAgIDwvYm9keT5cbiAgICAgIDwvSHRtbD5cbiAgICApO1xuICB9XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJEb2N1bWVudCIsIkh0bWwiLCJIZWFkIiwiTWFpbiIsIk5leHRTY3JpcHQiLCJjcmVhdGVFbW90aW9uQ2FjaGUiLCJjcmVhdGVFbW90aW9uU2VydmVyIiwiTXlEb2N1bWVudCIsImdldEluaXRpYWxQcm9wcyIsImN0eCIsIm9yaWdpbmFsUmVuZGVyUGFnZSIsInJlbmRlclBhZ2UiLCJjYWNoZSIsImV4dHJhY3RDcml0aWNhbFRvQ2h1bmtzIiwiZW5oYW5jZUFwcCIsIkFwcCIsIkVuaGFuY2VBcHAiLCJwcm9wcyIsImVtb3Rpb25DYWNoZSIsImluaXRpYWxQcm9wcyIsImVtb3Rpb25TdHlsZXMiLCJodG1sIiwiZW1vdGlvblN0eWxlVGFncyIsInN0eWxlcyIsIm1hcCIsInN0eWxlIiwiZGF0YS1lbW90aW9uIiwia2V5IiwiaWRzIiwiam9pbiIsImRhbmdlcm91c2x5U2V0SW5uZXJIVE1MIiwiX19odG1sIiwiY3NzIiwicmVuZGVyIiwibGFuZyIsIm1ldGEiLCJjaGFyU2V0IiwibmFtZSIsImNvbnRlbnQiLCJwcm9wZXJ0eSIsImxpbmsiLCJyZWwiLCJocmVmIiwidHlwZSIsInNpemVzIiwiY3Jvc3NPcmlnaW4iLCJzY3JpcHQiLCJKU09OIiwic3RyaW5naWZ5IiwiYm9keSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./pages/_document.tsx\n");

/***/ }),

/***/ "./utils/createEmotionCache.ts":
/*!*************************************!*\
  !*** ./utils/createEmotionCache.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createEmotionCache: () => (/* binding */ createEmotionCache),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _emotion_cache__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @emotion/cache */ \"@emotion/cache\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_emotion_cache__WEBPACK_IMPORTED_MODULE_0__]);\n_emotion_cache__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nconst isBrowser = typeof document !== \"undefined\";\n// On the client side, Create a meta tag at the top of the <head> and set it as insertionPoint.\n// This assures that MUI styles are loaded first.\n// It allows developers to easily override MUI styles with other styling solutions, like CSS modules.\nfunction createEmotionCache() {\n    let insertionPoint;\n    if (isBrowser) {\n        const emotionInsertionPoint = document.querySelector('meta[name=\"emotion-insertion-point\"]');\n        insertionPoint = emotionInsertionPoint ?? undefined;\n    }\n    return (0,_emotion_cache__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n        key: \"mui-style\",\n        insertionPoint\n    });\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (createEmotionCache);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi91dGlscy9jcmVhdGVFbW90aW9uQ2FjaGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQXlDO0FBRXpDLE1BQU1DLFlBQVksT0FBT0MsYUFBYTtBQUV0QywrRkFBK0Y7QUFDL0YsaURBQWlEO0FBQ2pELHFHQUFxRztBQUNyRyxTQUFTQztJQUNQLElBQUlDO0lBRUosSUFBSUgsV0FBVztRQUNiLE1BQU1JLHdCQUF3QkgsU0FBU0ksYUFBYSxDQUNsRDtRQUVGRixpQkFBaUJDLHlCQUF5QkU7SUFDNUM7SUFFQSxPQUFPUCwwREFBV0EsQ0FBQztRQUFFUSxLQUFLO1FBQWFKO0lBQWU7QUFDeEQ7QUFFOEI7QUFDOUIsaUVBQWVELGtCQUFrQkEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL3NvbGFuYS10cmFkaW5nLWRhc2hib2FyZC8uL3V0aWxzL2NyZWF0ZUVtb3Rpb25DYWNoZS50cz8xY2U2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVDYWNoZSBmcm9tICdAZW1vdGlvbi9jYWNoZSc7XG5cbmNvbnN0IGlzQnJvd3NlciA9IHR5cGVvZiBkb2N1bWVudCAhPT0gJ3VuZGVmaW5lZCc7XG5cbi8vIE9uIHRoZSBjbGllbnQgc2lkZSwgQ3JlYXRlIGEgbWV0YSB0YWcgYXQgdGhlIHRvcCBvZiB0aGUgPGhlYWQ+IGFuZCBzZXQgaXQgYXMgaW5zZXJ0aW9uUG9pbnQuXG4vLyBUaGlzIGFzc3VyZXMgdGhhdCBNVUkgc3R5bGVzIGFyZSBsb2FkZWQgZmlyc3QuXG4vLyBJdCBhbGxvd3MgZGV2ZWxvcGVycyB0byBlYXNpbHkgb3ZlcnJpZGUgTVVJIHN0eWxlcyB3aXRoIG90aGVyIHN0eWxpbmcgc29sdXRpb25zLCBsaWtlIENTUyBtb2R1bGVzLlxuZnVuY3Rpb24gY3JlYXRlRW1vdGlvbkNhY2hlKCkge1xuICBsZXQgaW5zZXJ0aW9uUG9pbnQ7XG5cbiAgaWYgKGlzQnJvd3Nlcikge1xuICAgIGNvbnN0IGVtb3Rpb25JbnNlcnRpb25Qb2ludCA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3I8SFRNTE1ldGFFbGVtZW50PihcbiAgICAgICdtZXRhW25hbWU9XCJlbW90aW9uLWluc2VydGlvbi1wb2ludFwiXScsXG4gICAgKTtcbiAgICBpbnNlcnRpb25Qb2ludCA9IGVtb3Rpb25JbnNlcnRpb25Qb2ludCA/PyB1bmRlZmluZWQ7XG4gIH1cblxuICByZXR1cm4gY3JlYXRlQ2FjaGUoeyBrZXk6ICdtdWktc3R5bGUnLCBpbnNlcnRpb25Qb2ludCB9KTtcbn1cblxuZXhwb3J0IHsgY3JlYXRlRW1vdGlvbkNhY2hlIH07XG5leHBvcnQgZGVmYXVsdCBjcmVhdGVFbW90aW9uQ2FjaGU7XG4iXSwibmFtZXMiOlsiY3JlYXRlQ2FjaGUiLCJpc0Jyb3dzZXIiLCJkb2N1bWVudCIsImNyZWF0ZUVtb3Rpb25DYWNoZSIsImluc2VydGlvblBvaW50IiwiZW1vdGlvbkluc2VydGlvblBvaW50IiwicXVlcnlTZWxlY3RvciIsInVuZGVmaW5lZCIsImtleSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./utils/createEmotionCache.ts\n");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "@emotion/cache":
/*!*********************************!*\
  !*** external "@emotion/cache" ***!
  \*********************************/
/***/ ((module) => {

module.exports = import("@emotion/cache");;

/***/ }),

/***/ "@emotion/server/create-instance":
/*!**************************************************!*\
  !*** external "@emotion/server/create-instance" ***!
  \**************************************************/
/***/ ((module) => {

module.exports = import("@emotion/server/create-instance");;

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./pages/_document.tsx")));
module.exports = __webpack_exports__;

})();