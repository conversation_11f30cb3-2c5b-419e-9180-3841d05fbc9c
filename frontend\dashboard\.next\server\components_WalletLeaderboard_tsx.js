"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "components_WalletLeaderboard_tsx";
exports.ids = ["components_WalletLeaderboard_tsx"];
exports.modules = {

/***/ "__barrel_optimize__?names=Avatar,Box,Chip,Divider,List,ListItem,ListItemAvatar,ListItemText,Typography!=!../../node_modules/@mui/material/index.js":
/*!**********************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Avatar,Box,Chip,Divider,List,ListItem,ListItemAvatar,ListItemText,Typography!=!../../node_modules/@mui/material/index.js ***!
  \**********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Avatar: () => (/* reexport default from dynamic */ _Avatar__WEBPACK_IMPORTED_MODULE_0___default.a),\n/* harmony export */   Box: () => (/* reexport default from dynamic */ _Box__WEBPACK_IMPORTED_MODULE_1___default.a),\n/* harmony export */   Chip: () => (/* reexport default from dynamic */ _Chip__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   Divider: () => (/* reexport default from dynamic */ _Divider__WEBPACK_IMPORTED_MODULE_3___default.a),\n/* harmony export */   List: () => (/* reexport default from dynamic */ _List__WEBPACK_IMPORTED_MODULE_4___default.a),\n/* harmony export */   ListItem: () => (/* reexport default from dynamic */ _ListItem__WEBPACK_IMPORTED_MODULE_5___default.a),\n/* harmony export */   ListItemAvatar: () => (/* reexport default from dynamic */ _ListItemAvatar__WEBPACK_IMPORTED_MODULE_6___default.a),\n/* harmony export */   ListItemText: () => (/* reexport default from dynamic */ _ListItemText__WEBPACK_IMPORTED_MODULE_7___default.a),\n/* harmony export */   Typography: () => (/* reexport default from dynamic */ _Typography__WEBPACK_IMPORTED_MODULE_8___default.a)\n/* harmony export */ });\n/* harmony import */ var _Avatar__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Avatar */ \"../../node_modules/@mui/material/node/Avatar/index.js\");\n/* harmony import */ var _Avatar__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Avatar__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Box__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Box */ \"../../node_modules/@mui/material/node/Box/index.js\");\n/* harmony import */ var _Box__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_Box__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Chip__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Chip */ \"../../node_modules/@mui/material/node/Chip/index.js\");\n/* harmony import */ var _Chip__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_Chip__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Divider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Divider */ \"../../node_modules/@mui/material/node/Divider/index.js\");\n/* harmony import */ var _Divider__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_Divider__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _List__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./List */ \"../../node_modules/@mui/material/node/List/index.js\");\n/* harmony import */ var _List__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_List__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _ListItem__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ListItem */ \"../../node_modules/@mui/material/node/ListItem/index.js\");\n/* harmony import */ var _ListItem__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_ListItem__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _ListItemAvatar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ListItemAvatar */ \"../../node_modules/@mui/material/node/ListItemAvatar/index.js\");\n/* harmony import */ var _ListItemAvatar__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_ListItemAvatar__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _ListItemText__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ListItemText */ \"../../node_modules/@mui/material/node/ListItemText/index.js\");\n/* harmony import */ var _ListItemText__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_ListItemText__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _Typography__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./Typography */ \"../../node_modules/@mui/material/node/Typography/index.js\");\n/* harmony import */ var _Typography__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_Typography__WEBPACK_IMPORTED_MODULE_8__);\n/**\n * @mui/material v5.18.0\n *\n * @license MIT\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */ /* eslint-disable import/export */ \n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BdmF0YXIsQm94LENoaXAsRGl2aWRlcixMaXN0LExpc3RJdGVtLExpc3RJdGVtQXZhdGFyLExpc3RJdGVtVGV4dCxUeXBvZ3JhcGh5IT0hLi4vLi4vbm9kZV9tb2R1bGVzL0BtdWkvbWF0ZXJpYWwvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDNEM7QUFDTjtBQUNFO0FBQ007QUFDTjtBQUNRO0FBQ1k7QUFDSiIsInNvdXJjZXMiOlsid2VicGFjazovL3NvbGFuYS10cmFkaW5nLWRhc2hib2FyZC8uLi8uLi9ub2RlX21vZHVsZXMvQG11aS9tYXRlcmlhbC9pbmRleC5qcz81MjgwIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQG11aS9tYXRlcmlhbCB2NS4xOC4wXG4gKlxuICogQGxpY2Vuc2UgTUlUXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZSBmb3VuZCBpbiB0aGVcbiAqIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqLyAvKiBlc2xpbnQtZGlzYWJsZSBpbXBvcnQvZXhwb3J0ICovIFxuZXhwb3J0IHsgZGVmYXVsdCBhcyBBdmF0YXIgfSBmcm9tIFwiLi9BdmF0YXJcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCb3ggfSBmcm9tIFwiLi9Cb3hcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDaGlwIH0gZnJvbSBcIi4vQ2hpcFwiXG5leHBvcnQgeyBkZWZhdWx0IGFzIERpdmlkZXIgfSBmcm9tIFwiLi9EaXZpZGVyXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTGlzdCB9IGZyb20gXCIuL0xpc3RcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBMaXN0SXRlbSB9IGZyb20gXCIuL0xpc3RJdGVtXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTGlzdEl0ZW1BdmF0YXIgfSBmcm9tIFwiLi9MaXN0SXRlbUF2YXRhclwiXG5leHBvcnQgeyBkZWZhdWx0IGFzIExpc3RJdGVtVGV4dCB9IGZyb20gXCIuL0xpc3RJdGVtVGV4dFwiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFR5cG9ncmFwaHkgfSBmcm9tIFwiLi9UeXBvZ3JhcGh5XCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Avatar,Box,Chip,Divider,List,ListItem,ListItemAvatar,ListItemText,Typography!=!../../node_modules/@mui/material/index.js\n");

/***/ }),

/***/ "./components/WalletLeaderboard.tsx":
/*!******************************************!*\
  !*** ./components/WalletLeaderboard.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_List_ListItem_ListItemAvatar_ListItemText_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,List,ListItem,ListItemAvatar,ListItemText,Typography!=!@mui/material */ \"__barrel_optimize__?names=Avatar,Box,Chip,Divider,List,ListItem,ListItemAvatar,ListItemText,Typography!=!../../node_modules/@mui/material/index.js\");\n/* harmony import */ var _mui_icons_material_AccountBalanceWallet__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mui/icons-material/AccountBalanceWallet */ \"@mui/icons-material/AccountBalanceWallet\");\n/* harmony import */ var _mui_icons_material_AccountBalanceWallet__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_mui_icons_material_AccountBalanceWallet__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _mui_icons_material_TrendingUp__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/icons-material/TrendingUp */ \"@mui/icons-material/TrendingUp\");\n/* harmony import */ var _mui_icons_material_TrendingUp__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_mui_icons_material_TrendingUp__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _mui_icons_material_TrendingDown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/icons-material/TrendingDown */ \"@mui/icons-material/TrendingDown\");\n/* harmony import */ var _mui_icons_material_TrendingDown__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_mui_icons_material_TrendingDown__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\n\nconst WalletLeaderboard = ()=>{\n    // Mock data for demonstration\n    const mockWallets = [\n        {\n            address: \"7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU\",\n            totalPnl: 125000.50,\n            winRate: 0.78,\n            totalTrades: 1250,\n            totalVolume: 2500000.00\n        },\n        {\n            address: \"9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM\",\n            totalPnl: 89000.25,\n            winRate: 0.65,\n            totalTrades: 890,\n            totalVolume: 1800000.00\n        },\n        {\n            address: \"BQcdHdAQW1hczDbBi9hiegXAR7A98Q9jx3X3iBBBDiq4\",\n            totalPnl: 67500.75,\n            winRate: 0.72,\n            totalTrades: 567,\n            totalVolume: 1200000.00\n        },\n        {\n            address: \"DhJ4hdhJSkQyQGC9MjjLykbs4RGGDrvkayJA3S8PiQdG\",\n            totalPnl: 45000.00,\n            winRate: 0.58,\n            totalTrades: 423,\n            totalVolume: 890000.00\n        },\n        {\n            address: \"EhYXQP4gp7w9QxfGz2McqHoUDufxm9BFamkVYXakrxMf\",\n            totalPnl: 32000.25,\n            winRate: 0.69,\n            totalTrades: 234,\n            totalVolume: 650000.00\n        }\n    ];\n    const formatCurrency = (value)=>{\n        return new Intl.NumberFormat(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\",\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 0\n        }).format(value);\n    };\n    const formatPercentage = (value)=>{\n        return `${(value * 100).toFixed(1)}%`;\n    };\n    const formatAddress = (address)=>{\n        return `${address.slice(0, 4)}...${address.slice(-4)}`;\n    };\n    const getPnlColor = (pnl)=>{\n        return pnl >= 0 ? \"success\" : \"error\";\n    };\n    const getPnlIcon = (pnl)=>{\n        return pnl >= 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_icons_material_TrendingUp__WEBPACK_IMPORTED_MODULE_3___default()), {}, void 0, false, {\n            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\WalletLeaderboard.tsx\",\n            lineNumber: 89,\n            columnNumber: 23\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_icons_material_TrendingDown__WEBPACK_IMPORTED_MODULE_4___default()), {}, void 0, false, {\n            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\WalletLeaderboard.tsx\",\n            lineNumber: 89,\n            columnNumber: 40\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_List_ListItem_ListItemAvatar_ListItemText_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__.Box, {\n        sx: {\n            maxHeight: 400,\n            overflow: \"auto\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_List_ListItem_ListItemAvatar_ListItemText_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__.List, {\n            children: mockWallets.map((wallet, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_List_ListItem_ListItemAvatar_ListItemText_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__.ListItem, {\n                            alignItems: \"flex-start\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_List_ListItem_ListItemAvatar_ListItemText_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__.ListItemAvatar, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_List_ListItem_ListItemAvatar_ListItemText_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                        sx: {\n                                            bgcolor: \"primary.main\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_icons_material_AccountBalanceWallet__WEBPACK_IMPORTED_MODULE_2___default()), {}, void 0, false, {\n                                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\WalletLeaderboard.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\WalletLeaderboard.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\WalletLeaderboard.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_List_ListItem_ListItemAvatar_ListItemText_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__.ListItemText, {\n                                    primary: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_List_ListItem_ListItemAvatar_ListItemText_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"space-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_List_ListItem_ListItemAvatar_ListItemText_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__.Typography, {\n                                                variant: \"subtitle1\",\n                                                fontWeight: \"bold\",\n                                                children: [\n                                                    \"#\",\n                                                    index + 1,\n                                                    \" \",\n                                                    formatAddress(wallet.address)\n                                                ]\n                                            }, void 0, true, void 0, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_List_ListItem_ListItemAvatar_ListItemText_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__.Chip, {\n                                                icon: getPnlIcon(wallet.totalPnl),\n                                                label: formatCurrency(wallet.totalPnl),\n                                                color: getPnlColor(wallet.totalPnl),\n                                                size: \"small\"\n                                            }, void 0, false, void 0, void 0)\n                                        ]\n                                    }, void 0, true, void 0, void 0),\n                                    secondary: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_List_ListItem_ListItemAvatar_ListItemText_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                        mt: 1,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_List_ListItem_ListItemAvatar_ListItemText_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                                display: \"flex\",\n                                                justifyContent: \"space-between\",\n                                                mb: 0.5,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_List_ListItem_ListItemAvatar_ListItemText_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__.Typography, {\n                                                        variant: \"body2\",\n                                                        color: \"text.secondary\",\n                                                        children: [\n                                                            \"Win Rate: \",\n                                                            formatPercentage(wallet.winRate)\n                                                        ]\n                                                    }, void 0, true, void 0, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_List_ListItem_ListItemAvatar_ListItemText_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__.Typography, {\n                                                        variant: \"body2\",\n                                                        color: \"text.secondary\",\n                                                        children: [\n                                                            \"Trades: \",\n                                                            wallet.totalTrades.toLocaleString()\n                                                        ]\n                                                    }, void 0, true, void 0, void 0)\n                                                ]\n                                            }, void 0, true, void 0, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_List_ListItem_ListItemAvatar_ListItemText_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__.Typography, {\n                                                variant: \"body2\",\n                                                color: \"text.secondary\",\n                                                children: [\n                                                    \"Volume: \",\n                                                    formatCurrency(wallet.totalVolume)\n                                                ]\n                                            }, void 0, true, void 0, void 0)\n                                        ]\n                                    }, void 0, true, void 0, void 0)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\WalletLeaderboard.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\WalletLeaderboard.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 13\n                        }, undefined),\n                        index < mockWallets.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_List_ListItem_ListItemAvatar_ListItemText_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__.Divider, {\n                            variant: \"inset\",\n                            component: \"li\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\WalletLeaderboard.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 48\n                        }, undefined)\n                    ]\n                }, wallet.address, true, {\n                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\WalletLeaderboard.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 11\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\WalletLeaderboard.tsx\",\n            lineNumber: 94,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\WalletLeaderboard.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (WalletLeaderboard);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/WalletLeaderboard.tsx\n");

/***/ })

};
;