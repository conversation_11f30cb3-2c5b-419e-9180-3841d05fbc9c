"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "components_AlertsPanel_tsx";
exports.ids = ["components_AlertsPanel_tsx"];
exports.modules = {

/***/ "__barrel_optimize__?names=Alert,AlertTitle,Box,Chip,List,ListItem,ListItemIcon,ListItemText,Typography!=!../../node_modules/@mui/material/index.js":
/*!**********************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Alert,AlertTitle,Box,Chip,List,ListItem,ListItemIcon,ListItemText,Typography!=!../../node_modules/@mui/material/index.js ***!
  \**********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Alert: () => (/* reexport default from dynamic */ _Alert__WEBPACK_IMPORTED_MODULE_0___default.a),\n/* harmony export */   AlertTitle: () => (/* reexport default from dynamic */ _AlertTitle__WEBPACK_IMPORTED_MODULE_1___default.a),\n/* harmony export */   Box: () => (/* reexport default from dynamic */ _Box__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   Chip: () => (/* reexport default from dynamic */ _Chip__WEBPACK_IMPORTED_MODULE_3___default.a),\n/* harmony export */   List: () => (/* reexport default from dynamic */ _List__WEBPACK_IMPORTED_MODULE_4___default.a),\n/* harmony export */   ListItem: () => (/* reexport default from dynamic */ _ListItem__WEBPACK_IMPORTED_MODULE_5___default.a),\n/* harmony export */   ListItemIcon: () => (/* reexport default from dynamic */ _ListItemIcon__WEBPACK_IMPORTED_MODULE_6___default.a),\n/* harmony export */   ListItemText: () => (/* reexport default from dynamic */ _ListItemText__WEBPACK_IMPORTED_MODULE_7___default.a),\n/* harmony export */   Typography: () => (/* reexport default from dynamic */ _Typography__WEBPACK_IMPORTED_MODULE_8___default.a)\n/* harmony export */ });\n/* harmony import */ var _Alert__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Alert */ \"../../node_modules/@mui/material/node/Alert/index.js\");\n/* harmony import */ var _Alert__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Alert__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _AlertTitle__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./AlertTitle */ \"../../node_modules/@mui/material/node/AlertTitle/index.js\");\n/* harmony import */ var _AlertTitle__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_AlertTitle__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Box__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Box */ \"../../node_modules/@mui/material/node/Box/index.js\");\n/* harmony import */ var _Box__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_Box__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Chip__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Chip */ \"../../node_modules/@mui/material/node/Chip/index.js\");\n/* harmony import */ var _Chip__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_Chip__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _List__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./List */ \"../../node_modules/@mui/material/node/List/index.js\");\n/* harmony import */ var _List__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_List__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _ListItem__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ListItem */ \"../../node_modules/@mui/material/node/ListItem/index.js\");\n/* harmony import */ var _ListItem__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_ListItem__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _ListItemIcon__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ListItemIcon */ \"../../node_modules/@mui/material/node/ListItemIcon/index.js\");\n/* harmony import */ var _ListItemIcon__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_ListItemIcon__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _ListItemText__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ListItemText */ \"../../node_modules/@mui/material/node/ListItemText/index.js\");\n/* harmony import */ var _ListItemText__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_ListItemText__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _Typography__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./Typography */ \"../../node_modules/@mui/material/node/Typography/index.js\");\n/* harmony import */ var _Typography__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_Typography__WEBPACK_IMPORTED_MODULE_8__);\n/**\n * @mui/material v5.18.0\n *\n * @license MIT\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */ /* eslint-disable import/export */ \n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BbGVydCxBbGVydFRpdGxlLEJveCxDaGlwLExpc3QsTGlzdEl0ZW0sTGlzdEl0ZW1JY29uLExpc3RJdGVtVGV4dCxUeXBvZ3JhcGh5IT0hLi4vLi4vbm9kZV9tb2R1bGVzL0BtdWkvbWF0ZXJpYWwvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDMEM7QUFDVTtBQUNkO0FBQ0U7QUFDQTtBQUNRO0FBQ1E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3NvbGFuYS10cmFkaW5nLWRhc2hib2FyZC8uLi8uLi9ub2RlX21vZHVsZXMvQG11aS9tYXRlcmlhbC9pbmRleC5qcz84Zjk4Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQG11aS9tYXRlcmlhbCB2NS4xOC4wXG4gKlxuICogQGxpY2Vuc2UgTUlUXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZSBmb3VuZCBpbiB0aGVcbiAqIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqLyAvKiBlc2xpbnQtZGlzYWJsZSBpbXBvcnQvZXhwb3J0ICovIFxuZXhwb3J0IHsgZGVmYXVsdCBhcyBBbGVydCB9IGZyb20gXCIuL0FsZXJ0XCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQWxlcnRUaXRsZSB9IGZyb20gXCIuL0FsZXJ0VGl0bGVcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCb3ggfSBmcm9tIFwiLi9Cb3hcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDaGlwIH0gZnJvbSBcIi4vQ2hpcFwiXG5leHBvcnQgeyBkZWZhdWx0IGFzIExpc3QgfSBmcm9tIFwiLi9MaXN0XCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTGlzdEl0ZW0gfSBmcm9tIFwiLi9MaXN0SXRlbVwiXG5leHBvcnQgeyBkZWZhdWx0IGFzIExpc3RJdGVtSWNvbiB9IGZyb20gXCIuL0xpc3RJdGVtSWNvblwiXG5leHBvcnQgeyBkZWZhdWx0IGFzIExpc3RJdGVtVGV4dCB9IGZyb20gXCIuL0xpc3RJdGVtVGV4dFwiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFR5cG9ncmFwaHkgfSBmcm9tIFwiLi9UeXBvZ3JhcGh5XCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Alert,AlertTitle,Box,Chip,List,ListItem,ListItemIcon,ListItemText,Typography!=!../../node_modules/@mui/material/index.js\n");

/***/ }),

/***/ "./components/AlertsPanel.tsx":
/*!************************************!*\
  !*** ./components/AlertsPanel.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_AlertTitle_Box_Chip_List_ListItem_ListItemIcon_ListItemText_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,AlertTitle,Box,Chip,List,ListItem,ListItemIcon,ListItemText,Typography!=!@mui/material */ \"__barrel_optimize__?names=Alert,AlertTitle,Box,Chip,List,ListItem,ListItemIcon,ListItemText,Typography!=!../../node_modules/@mui/material/index.js\");\n/* harmony import */ var _mui_icons_material_Warning__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mui/icons-material/Warning */ \"@mui/icons-material/Warning\");\n/* harmony import */ var _mui_icons_material_Warning__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_mui_icons_material_Warning__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/icons-material/Error */ \"@mui/icons-material/Error\");\n/* harmony import */ var _mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _mui_icons_material_Info__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/icons-material/Info */ \"@mui/icons-material/Info\");\n/* harmony import */ var _mui_icons_material_Info__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_mui_icons_material_Info__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mui/icons-material/CheckCircle */ \"@mui/icons-material/CheckCircle\");\n/* harmony import */ var _mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _mui_icons_material_Security__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mui/icons-material/Security */ \"@mui/icons-material/Security\");\n/* harmony import */ var _mui_icons_material_Security__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_mui_icons_material_Security__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _mui_icons_material_TrendingUp__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @mui/icons-material/TrendingUp */ \"@mui/icons-material/TrendingUp\");\n/* harmony import */ var _mui_icons_material_TrendingUp__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_mui_icons_material_TrendingUp__WEBPACK_IMPORTED_MODULE_7__);\n\n\n\n\n\n\n\n\n\nconst AlertsPanel = ({ alerts })=>{\n    // Mock data for demonstration\n    const mockAlerts = [\n        {\n            id: \"1\",\n            type: \"whale_movement\",\n            severity: \"high\",\n            title: \"Large Whale Movement Detected\",\n            description: \"Wallet moved 50,000 SOL to unknown address\",\n            timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),\n            isResolved: false\n        },\n        {\n            id: \"2\",\n            type: \"volume_spike\",\n            severity: \"medium\",\n            title: \"Volume Spike Alert\",\n            description: \"BONK trading volume increased by 300% in last hour\",\n            timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),\n            isResolved: false\n        },\n        {\n            id: \"3\",\n            type: \"suspicious_activity\",\n            severity: \"critical\",\n            title: \"Suspicious Trading Pattern\",\n            description: \"Coordinated selling detected across multiple wallets\",\n            timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),\n            isResolved: false\n        },\n        {\n            id: \"4\",\n            type: \"price_alert\",\n            severity: \"low\",\n            title: \"Price Target Reached\",\n            description: \"SOL reached $150 resistance level\",\n            timestamp: new Date(Date.now() - 45 * 60 * 1000).toISOString(),\n            isResolved: true\n        }\n    ];\n    const displayAlerts = alerts.length > 0 ? alerts : mockAlerts;\n    const getAlertIcon = (type)=>{\n        switch(type){\n            case \"whale_movement\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_icons_material_TrendingUp__WEBPACK_IMPORTED_MODULE_7___default()), {}, void 0, false, {\n                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\AlertsPanel.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 16\n                }, undefined);\n            case \"volume_spike\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_icons_material_TrendingUp__WEBPACK_IMPORTED_MODULE_7___default()), {}, void 0, false, {\n                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\AlertsPanel.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 16\n                }, undefined);\n            case \"rugpull_detected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_icons_material_Error__WEBPACK_IMPORTED_MODULE_3___default()), {}, void 0, false, {\n                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\AlertsPanel.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 16\n                }, undefined);\n            case \"suspicious_activity\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_icons_material_Security__WEBPACK_IMPORTED_MODULE_6___default()), {}, void 0, false, {\n                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\AlertsPanel.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 16\n                }, undefined);\n            case \"price_alert\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_icons_material_Info__WEBPACK_IMPORTED_MODULE_4___default()), {}, void 0, false, {\n                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\AlertsPanel.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_icons_material_Warning__WEBPACK_IMPORTED_MODULE_2___default()), {}, void 0, false, {\n                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\AlertsPanel.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const getSeverityColor = (severity)=>{\n        switch(severity){\n            case \"low\":\n                return \"info\";\n            case \"medium\":\n                return \"warning\";\n            case \"high\":\n                return \"error\";\n            case \"critical\":\n                return \"error\";\n            default:\n                return \"default\";\n        }\n    };\n    const getAlertSeverity = (severity)=>{\n        switch(severity){\n            case \"low\":\n                return \"info\";\n            case \"medium\":\n                return \"warning\";\n            case \"high\":\n                return \"error\";\n            case \"critical\":\n                return \"error\";\n            default:\n                return \"info\";\n        }\n    };\n    const formatTimeAgo = (timestamp)=>{\n        const now = new Date();\n        const alertTime = new Date(timestamp);\n        const diffInMinutes = Math.floor((now.getTime() - alertTime.getTime()) / (1000 * 60));\n        if (diffInMinutes < 1) return \"Just now\";\n        if (diffInMinutes < 60) return `${diffInMinutes}m ago`;\n        const diffInHours = Math.floor(diffInMinutes / 60);\n        if (diffInHours < 24) return `${diffInHours}h ago`;\n        const diffInDays = Math.floor(diffInHours / 24);\n        return `${diffInDays}d ago`;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertTitle_Box_Chip_List_ListItem_ListItemIcon_ListItemText_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__.Box, {\n        sx: {\n            maxHeight: 350,\n            overflow: \"auto\"\n        },\n        children: displayAlerts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertTitle_Box_Chip_List_ListItem_ListItemIcon_ListItemText_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__.Alert, {\n            severity: \"success\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertTitle_Box_Chip_List_ListItem_ListItemIcon_ListItemText_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__.AlertTitle, {\n                    children: \"All Clear\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\AlertsPanel.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 11\n                }, undefined),\n                \"No active alerts at this time.\"\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\AlertsPanel.tsx\",\n            lineNumber: 144,\n            columnNumber: 9\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertTitle_Box_Chip_List_ListItem_ListItemIcon_ListItemText_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__.List, {\n            dense: true,\n            children: displayAlerts.slice(0, 10).map((alert)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertTitle_Box_Chip_List_ListItem_ListItemIcon_ListItemText_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__.ListItem, {\n                    sx: {\n                        mb: 1,\n                        border: 1,\n                        borderColor: \"divider\",\n                        borderRadius: 1,\n                        bgcolor: alert.isResolved ? \"grey.50\" : \"background.paper\",\n                        opacity: alert.isResolved ? 0.7 : 1\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertTitle_Box_Chip_List_ListItem_ListItemIcon_ListItemText_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__.ListItemIcon, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertTitle_Box_Chip_List_ListItem_ListItemIcon_ListItemText_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__.Box, {\n                                color: `${getSeverityColor(alert.severity)}.main`,\n                                children: getAlertIcon(alert.type)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\AlertsPanel.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\AlertsPanel.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertTitle_Box_Chip_List_ListItem_ListItemIcon_ListItemText_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__.ListItemText, {\n                            primary: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertTitle_Box_Chip_List_ListItem_ListItemIcon_ListItemText_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__.Box, {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"space-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertTitle_Box_Chip_List_ListItem_ListItemIcon_ListItemText_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__.Typography, {\n                                        variant: \"subtitle2\",\n                                        fontWeight: \"bold\",\n                                        children: alert.title\n                                    }, void 0, false, void 0, void 0),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertTitle_Box_Chip_List_ListItem_ListItemIcon_ListItemText_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__.Box, {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        gap: 1,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertTitle_Box_Chip_List_ListItem_ListItemIcon_ListItemText_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__.Chip, {\n                                                label: alert.severity.toUpperCase(),\n                                                color: getSeverityColor(alert.severity),\n                                                size: \"small\",\n                                                variant: \"outlined\"\n                                            }, void 0, false, void 0, void 0),\n                                            alert.isResolved && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_icons_material_CheckCircle__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                color: \"success\",\n                                                fontSize: \"small\"\n                                            }, void 0, false, void 0, void 0)\n                                        ]\n                                    }, void 0, true, void 0, void 0)\n                                ]\n                            }, void 0, true, void 0, void 0),\n                            secondary: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertTitle_Box_Chip_List_ListItem_ListItemIcon_ListItemText_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__.Box, {\n                                mt: 0.5,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertTitle_Box_Chip_List_ListItem_ListItemIcon_ListItemText_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__.Typography, {\n                                        variant: \"body2\",\n                                        color: \"text.secondary\",\n                                        mb: 0.5,\n                                        children: alert.description\n                                    }, void 0, false, void 0, void 0),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertTitle_Box_Chip_List_ListItem_ListItemIcon_ListItemText_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__.Typography, {\n                                        variant: \"caption\",\n                                        color: \"text.secondary\",\n                                        children: formatTimeAgo(alert.timestamp)\n                                    }, void 0, false, void 0, void 0)\n                                ]\n                            }, void 0, true, void 0, void 0)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\AlertsPanel.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, alert.id, true, {\n                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\AlertsPanel.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 13\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\AlertsPanel.tsx\",\n            lineNumber: 149,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\AlertsPanel.tsx\",\n        lineNumber: 142,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AlertsPanel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0FsZXJ0c1BhbmVsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQTBCO0FBV0g7QUFRTTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFnQjdCLE1BQU1nQixjQUEwQyxDQUFDLEVBQUVDLE1BQU0sRUFBRTtJQUN6RCw4QkFBOEI7SUFDOUIsTUFBTUMsYUFBMEI7UUFDOUI7WUFDRUMsSUFBSTtZQUNKQyxNQUFNO1lBQ05DLFVBQVU7WUFDVkMsT0FBTztZQUNQQyxhQUFhO1lBQ2JDLFdBQVcsSUFBSUMsS0FBS0EsS0FBS0MsR0FBRyxLQUFLLElBQUksS0FBSyxNQUFNQyxXQUFXO1lBQzNEQyxZQUFZO1FBQ2Q7UUFDQTtZQUNFVCxJQUFJO1lBQ0pDLE1BQU07WUFDTkMsVUFBVTtZQUNWQyxPQUFPO1lBQ1BDLGFBQWE7WUFDYkMsV0FBVyxJQUFJQyxLQUFLQSxLQUFLQyxHQUFHLEtBQUssS0FBSyxLQUFLLE1BQU1DLFdBQVc7WUFDNURDLFlBQVk7UUFDZDtRQUNBO1lBQ0VULElBQUk7WUFDSkMsTUFBTTtZQUNOQyxVQUFVO1lBQ1ZDLE9BQU87WUFDUEMsYUFBYTtZQUNiQyxXQUFXLElBQUlDLEtBQUtBLEtBQUtDLEdBQUcsS0FBSyxLQUFLLEtBQUssTUFBTUMsV0FBVztZQUM1REMsWUFBWTtRQUNkO1FBQ0E7WUFDRVQsSUFBSTtZQUNKQyxNQUFNO1lBQ05DLFVBQVU7WUFDVkMsT0FBTztZQUNQQyxhQUFhO1lBQ2JDLFdBQVcsSUFBSUMsS0FBS0EsS0FBS0MsR0FBRyxLQUFLLEtBQUssS0FBSyxNQUFNQyxXQUFXO1lBQzVEQyxZQUFZO1FBQ2Q7S0FDRDtJQUVELE1BQU1DLGdCQUFnQlosT0FBT2EsTUFBTSxHQUFHLElBQUliLFNBQVNDO0lBRW5ELE1BQU1hLGVBQWUsQ0FBQ1g7UUFDcEIsT0FBUUE7WUFDTixLQUFLO2dCQUNILHFCQUFPLDhEQUFDTCx1RUFBVUE7Ozs7O1lBQ3BCLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUNBLHVFQUFVQTs7Ozs7WUFDcEIsS0FBSztnQkFDSCxxQkFBTyw4REFBQ0osa0VBQUtBOzs7OztZQUNmLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUNHLHFFQUFRQTs7Ozs7WUFDbEIsS0FBSztnQkFDSCxxQkFBTyw4REFBQ0YsaUVBQUlBOzs7OztZQUNkO2dCQUNFLHFCQUFPLDhEQUFDRixvRUFBT0E7Ozs7O1FBQ25CO0lBQ0Y7SUFFQSxNQUFNc0IsbUJBQW1CLENBQUNYO1FBQ3hCLE9BQVFBO1lBQ04sS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1Q7Z0JBQ0UsT0FBTztRQUNYO0lBQ0Y7SUFFQSxNQUFNWSxtQkFBbUIsQ0FBQ1o7UUFDeEIsT0FBUUE7WUFDTixLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVDtnQkFDRSxPQUFPO1FBQ1g7SUFDRjtJQUVBLE1BQU1hLGdCQUFnQixDQUFDVjtRQUNyQixNQUFNRSxNQUFNLElBQUlEO1FBQ2hCLE1BQU1VLFlBQVksSUFBSVYsS0FBS0Q7UUFDM0IsTUFBTVksZ0JBQWdCQyxLQUFLQyxLQUFLLENBQUMsQ0FBQ1osSUFBSWEsT0FBTyxLQUFLSixVQUFVSSxPQUFPLEVBQUMsSUFBTSxRQUFPLEVBQUM7UUFFbEYsSUFBSUgsZ0JBQWdCLEdBQUcsT0FBTztRQUM5QixJQUFJQSxnQkFBZ0IsSUFBSSxPQUFPLENBQUMsRUFBRUEsY0FBYyxLQUFLLENBQUM7UUFFdEQsTUFBTUksY0FBY0gsS0FBS0MsS0FBSyxDQUFDRixnQkFBZ0I7UUFDL0MsSUFBSUksY0FBYyxJQUFJLE9BQU8sQ0FBQyxFQUFFQSxZQUFZLEtBQUssQ0FBQztRQUVsRCxNQUFNQyxhQUFhSixLQUFLQyxLQUFLLENBQUNFLGNBQWM7UUFDNUMsT0FBTyxDQUFDLEVBQUVDLFdBQVcsS0FBSyxDQUFDO0lBQzdCO0lBRUEscUJBQ0UsOERBQUNuQyxpSkFBR0E7UUFBQ29DLElBQUk7WUFBRUMsV0FBVztZQUFLQyxVQUFVO1FBQU87a0JBQ3pDZixjQUFjQyxNQUFNLEtBQUssa0JBQ3hCLDhEQUFDdEIsbUpBQUtBO1lBQUNhLFVBQVM7OzhCQUNkLDhEQUFDWix3SkFBVUE7OEJBQUM7Ozs7OztnQkFBc0I7Ozs7OztzQ0FJcEMsOERBQUNSLGtKQUFJQTtZQUFDNEMsS0FBSztzQkFDUmhCLGNBQWNpQixLQUFLLENBQUMsR0FBRyxJQUFJQyxHQUFHLENBQUMsQ0FBQ0Msc0JBQy9CLDhEQUFDOUMsc0pBQVFBO29CQUVQd0MsSUFBSTt3QkFDRk8sSUFBSTt3QkFDSkMsUUFBUTt3QkFDUkMsYUFBYTt3QkFDYkMsY0FBYzt3QkFDZEMsU0FBU0wsTUFBTXBCLFVBQVUsR0FBRyxZQUFZO3dCQUN4QzBCLFNBQVNOLE1BQU1wQixVQUFVLEdBQUcsTUFBTTtvQkFDcEM7O3NDQUVBLDhEQUFDeEIsMEpBQVlBO3NDQUNYLDRFQUFDRSxpSkFBR0E7Z0NBQUNpRCxPQUFPLENBQUMsRUFBRXZCLGlCQUFpQmdCLE1BQU0zQixRQUFRLEVBQUUsS0FBSyxDQUFDOzBDQUNuRFUsYUFBYWlCLE1BQU01QixJQUFJOzs7Ozs7Ozs7OztzQ0FHNUIsOERBQUNqQiwwSkFBWUE7NEJBQ1hxRCx1QkFDRSw4REFBQ2xELGlKQUFHQTtnQ0FBQ21ELFNBQVE7Z0NBQU9DLFlBQVc7Z0NBQVNDLGdCQUFlOztrREFDckQsOERBQUN0RCx3SkFBVUE7d0NBQUN1RCxTQUFRO3dDQUFZQyxZQUFXO2tEQUN4Q2IsTUFBTTFCLEtBQUs7O2tEQUVkLDhEQUFDaEIsaUpBQUdBO3dDQUFDbUQsU0FBUTt3Q0FBT0MsWUFBVzt3Q0FBU0ksS0FBSzs7MERBQzNDLDhEQUFDdkQsa0pBQUlBO2dEQUNId0QsT0FBT2YsTUFBTTNCLFFBQVEsQ0FBQzJDLFdBQVc7Z0RBQ2pDVCxPQUFPdkIsaUJBQWlCZ0IsTUFBTTNCLFFBQVE7Z0RBQ3RDNEMsTUFBSztnREFDTEwsU0FBUTs7NENBRVRaLE1BQU1wQixVQUFVLGtCQUNmLDhEQUFDZix3RUFBV0E7Z0RBQUMwQyxPQUFNO2dEQUFVVyxVQUFTOzs7Ozs7NEJBSzlDQyx5QkFDRSw4REFBQzdELGlKQUFHQTtnQ0FBQzhELElBQUk7O2tEQUNQLDhEQUFDL0Qsd0pBQVVBO3dDQUFDdUQsU0FBUTt3Q0FBUUwsT0FBTTt3Q0FBaUJOLElBQUk7a0RBQ3BERCxNQUFNekIsV0FBVzs7a0RBRXBCLDhEQUFDbEIsd0pBQVVBO3dDQUFDdUQsU0FBUTt3Q0FBVUwsT0FBTTtrREFDakNyQixjQUFjYyxNQUFNeEIsU0FBUzs7Ozs7Ozs7OzttQkF4Q2pDd0IsTUFBTTdCLEVBQUU7Ozs7Ozs7Ozs7Ozs7OztBQW1EM0I7QUFFQSxpRUFBZUgsV0FBV0EsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL3NvbGFuYS10cmFkaW5nLWRhc2hib2FyZC8uL2NvbXBvbmVudHMvQWxlcnRzUGFuZWwudHN4Pzg0YmMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7XG4gIExpc3QsXG4gIExpc3RJdGVtLFxuICBMaXN0SXRlbVRleHQsXG4gIExpc3RJdGVtSWNvbixcbiAgVHlwb2dyYXBoeSxcbiAgQm94LFxuICBDaGlwLFxuICBBbGVydCxcbiAgQWxlcnRUaXRsZSxcbn0gZnJvbSAnQG11aS9tYXRlcmlhbCc7XG5pbXBvcnQge1xuICBXYXJuaW5nLFxuICBFcnJvcixcbiAgSW5mbyxcbiAgQ2hlY2tDaXJjbGUsXG4gIFNlY3VyaXR5LFxuICBUcmVuZGluZ1VwLFxufSBmcm9tICdAbXVpL2ljb25zLW1hdGVyaWFsJztcblxuaW50ZXJmYWNlIEFsZXJ0RGF0YSB7XG4gIGlkOiBzdHJpbmc7XG4gIHR5cGU6ICd3aGFsZV9tb3ZlbWVudCcgfCAndm9sdW1lX3NwaWtlJyB8ICdydWdwdWxsX2RldGVjdGVkJyB8ICdzdXNwaWNpb3VzX2FjdGl2aXR5JyB8ICdwcmljZV9hbGVydCc7XG4gIHNldmVyaXR5OiAnbG93JyB8ICdtZWRpdW0nIHwgJ2hpZ2gnIHwgJ2NyaXRpY2FsJztcbiAgdGl0bGU6IHN0cmluZztcbiAgZGVzY3JpcHRpb246IHN0cmluZztcbiAgdGltZXN0YW1wOiBzdHJpbmc7XG4gIGlzUmVzb2x2ZWQ6IGJvb2xlYW47XG59XG5cbmludGVyZmFjZSBBbGVydHNQYW5lbFByb3BzIHtcbiAgYWxlcnRzOiBBbGVydERhdGFbXTtcbn1cblxuY29uc3QgQWxlcnRzUGFuZWw6IFJlYWN0LkZDPEFsZXJ0c1BhbmVsUHJvcHM+ID0gKHsgYWxlcnRzIH0pID0+IHtcbiAgLy8gTW9jayBkYXRhIGZvciBkZW1vbnN0cmF0aW9uXG4gIGNvbnN0IG1vY2tBbGVydHM6IEFsZXJ0RGF0YVtdID0gW1xuICAgIHtcbiAgICAgIGlkOiAnMScsXG4gICAgICB0eXBlOiAnd2hhbGVfbW92ZW1lbnQnLFxuICAgICAgc2V2ZXJpdHk6ICdoaWdoJyxcbiAgICAgIHRpdGxlOiAnTGFyZ2UgV2hhbGUgTW92ZW1lbnQgRGV0ZWN0ZWQnLFxuICAgICAgZGVzY3JpcHRpb246ICdXYWxsZXQgbW92ZWQgNTAsMDAwIFNPTCB0byB1bmtub3duIGFkZHJlc3MnLFxuICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZShEYXRlLm5vdygpIC0gNSAqIDYwICogMTAwMCkudG9JU09TdHJpbmcoKSxcbiAgICAgIGlzUmVzb2x2ZWQ6IGZhbHNlLFxuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICcyJyxcbiAgICAgIHR5cGU6ICd2b2x1bWVfc3Bpa2UnLFxuICAgICAgc2V2ZXJpdHk6ICdtZWRpdW0nLFxuICAgICAgdGl0bGU6ICdWb2x1bWUgU3Bpa2UgQWxlcnQnLFxuICAgICAgZGVzY3JpcHRpb246ICdCT05LIHRyYWRpbmcgdm9sdW1lIGluY3JlYXNlZCBieSAzMDAlIGluIGxhc3QgaG91cicsXG4gICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKERhdGUubm93KCkgLSAxNSAqIDYwICogMTAwMCkudG9JU09TdHJpbmcoKSxcbiAgICAgIGlzUmVzb2x2ZWQ6IGZhbHNlLFxuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICczJyxcbiAgICAgIHR5cGU6ICdzdXNwaWNpb3VzX2FjdGl2aXR5JyxcbiAgICAgIHNldmVyaXR5OiAnY3JpdGljYWwnLFxuICAgICAgdGl0bGU6ICdTdXNwaWNpb3VzIFRyYWRpbmcgUGF0dGVybicsXG4gICAgICBkZXNjcmlwdGlvbjogJ0Nvb3JkaW5hdGVkIHNlbGxpbmcgZGV0ZWN0ZWQgYWNyb3NzIG11bHRpcGxlIHdhbGxldHMnLFxuICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZShEYXRlLm5vdygpIC0gMzAgKiA2MCAqIDEwMDApLnRvSVNPU3RyaW5nKCksXG4gICAgICBpc1Jlc29sdmVkOiBmYWxzZSxcbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiAnNCcsXG4gICAgICB0eXBlOiAncHJpY2VfYWxlcnQnLFxuICAgICAgc2V2ZXJpdHk6ICdsb3cnLFxuICAgICAgdGl0bGU6ICdQcmljZSBUYXJnZXQgUmVhY2hlZCcsXG4gICAgICBkZXNjcmlwdGlvbjogJ1NPTCByZWFjaGVkICQxNTAgcmVzaXN0YW5jZSBsZXZlbCcsXG4gICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKERhdGUubm93KCkgLSA0NSAqIDYwICogMTAwMCkudG9JU09TdHJpbmcoKSxcbiAgICAgIGlzUmVzb2x2ZWQ6IHRydWUsXG4gICAgfSxcbiAgXTtcblxuICBjb25zdCBkaXNwbGF5QWxlcnRzID0gYWxlcnRzLmxlbmd0aCA+IDAgPyBhbGVydHMgOiBtb2NrQWxlcnRzO1xuXG4gIGNvbnN0IGdldEFsZXJ0SWNvbiA9ICh0eXBlOiBBbGVydERhdGFbJ3R5cGUnXSkgPT4ge1xuICAgIHN3aXRjaCAodHlwZSkge1xuICAgICAgY2FzZSAnd2hhbGVfbW92ZW1lbnQnOlxuICAgICAgICByZXR1cm4gPFRyZW5kaW5nVXAgLz47XG4gICAgICBjYXNlICd2b2x1bWVfc3Bpa2UnOlxuICAgICAgICByZXR1cm4gPFRyZW5kaW5nVXAgLz47XG4gICAgICBjYXNlICdydWdwdWxsX2RldGVjdGVkJzpcbiAgICAgICAgcmV0dXJuIDxFcnJvciAvPjtcbiAgICAgIGNhc2UgJ3N1c3BpY2lvdXNfYWN0aXZpdHknOlxuICAgICAgICByZXR1cm4gPFNlY3VyaXR5IC8+O1xuICAgICAgY2FzZSAncHJpY2VfYWxlcnQnOlxuICAgICAgICByZXR1cm4gPEluZm8gLz47XG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gPFdhcm5pbmcgLz47XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGdldFNldmVyaXR5Q29sb3IgPSAoc2V2ZXJpdHk6IEFsZXJ0RGF0YVsnc2V2ZXJpdHknXSkgPT4ge1xuICAgIHN3aXRjaCAoc2V2ZXJpdHkpIHtcbiAgICAgIGNhc2UgJ2xvdyc6XG4gICAgICAgIHJldHVybiAnaW5mbyc7XG4gICAgICBjYXNlICdtZWRpdW0nOlxuICAgICAgICByZXR1cm4gJ3dhcm5pbmcnO1xuICAgICAgY2FzZSAnaGlnaCc6XG4gICAgICAgIHJldHVybiAnZXJyb3InO1xuICAgICAgY2FzZSAnY3JpdGljYWwnOlxuICAgICAgICByZXR1cm4gJ2Vycm9yJztcbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHJldHVybiAnZGVmYXVsdCc7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGdldEFsZXJ0U2V2ZXJpdHkgPSAoc2V2ZXJpdHk6IEFsZXJ0RGF0YVsnc2V2ZXJpdHknXSkgPT4ge1xuICAgIHN3aXRjaCAoc2V2ZXJpdHkpIHtcbiAgICAgIGNhc2UgJ2xvdyc6XG4gICAgICAgIHJldHVybiAnaW5mbyc7XG4gICAgICBjYXNlICdtZWRpdW0nOlxuICAgICAgICByZXR1cm4gJ3dhcm5pbmcnO1xuICAgICAgY2FzZSAnaGlnaCc6XG4gICAgICAgIHJldHVybiAnZXJyb3InO1xuICAgICAgY2FzZSAnY3JpdGljYWwnOlxuICAgICAgICByZXR1cm4gJ2Vycm9yJztcbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHJldHVybiAnaW5mbyc7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGZvcm1hdFRpbWVBZ28gPSAodGltZXN0YW1wOiBzdHJpbmcpID0+IHtcbiAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpO1xuICAgIGNvbnN0IGFsZXJ0VGltZSA9IG5ldyBEYXRlKHRpbWVzdGFtcCk7XG4gICAgY29uc3QgZGlmZkluTWludXRlcyA9IE1hdGguZmxvb3IoKG5vdy5nZXRUaW1lKCkgLSBhbGVydFRpbWUuZ2V0VGltZSgpKSAvICgxMDAwICogNjApKTtcbiAgICBcbiAgICBpZiAoZGlmZkluTWludXRlcyA8IDEpIHJldHVybiAnSnVzdCBub3cnO1xuICAgIGlmIChkaWZmSW5NaW51dGVzIDwgNjApIHJldHVybiBgJHtkaWZmSW5NaW51dGVzfW0gYWdvYDtcbiAgICBcbiAgICBjb25zdCBkaWZmSW5Ib3VycyA9IE1hdGguZmxvb3IoZGlmZkluTWludXRlcyAvIDYwKTtcbiAgICBpZiAoZGlmZkluSG91cnMgPCAyNCkgcmV0dXJuIGAke2RpZmZJbkhvdXJzfWggYWdvYDtcbiAgICBcbiAgICBjb25zdCBkaWZmSW5EYXlzID0gTWF0aC5mbG9vcihkaWZmSW5Ib3VycyAvIDI0KTtcbiAgICByZXR1cm4gYCR7ZGlmZkluRGF5c31kIGFnb2A7XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8Qm94IHN4PXt7IG1heEhlaWdodDogMzUwLCBvdmVyZmxvdzogJ2F1dG8nIH19PlxuICAgICAge2Rpc3BsYXlBbGVydHMubGVuZ3RoID09PSAwID8gKFxuICAgICAgICA8QWxlcnQgc2V2ZXJpdHk9XCJzdWNjZXNzXCI+XG4gICAgICAgICAgPEFsZXJ0VGl0bGU+QWxsIENsZWFyPC9BbGVydFRpdGxlPlxuICAgICAgICAgIE5vIGFjdGl2ZSBhbGVydHMgYXQgdGhpcyB0aW1lLlxuICAgICAgICA8L0FsZXJ0PlxuICAgICAgKSA6IChcbiAgICAgICAgPExpc3QgZGVuc2U+XG4gICAgICAgICAge2Rpc3BsYXlBbGVydHMuc2xpY2UoMCwgMTApLm1hcCgoYWxlcnQpID0+IChcbiAgICAgICAgICAgIDxMaXN0SXRlbVxuICAgICAgICAgICAgICBrZXk9e2FsZXJ0LmlkfVxuICAgICAgICAgICAgICBzeD17e1xuICAgICAgICAgICAgICAgIG1iOiAxLFxuICAgICAgICAgICAgICAgIGJvcmRlcjogMSxcbiAgICAgICAgICAgICAgICBib3JkZXJDb2xvcjogJ2RpdmlkZXInLFxuICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogMSxcbiAgICAgICAgICAgICAgICBiZ2NvbG9yOiBhbGVydC5pc1Jlc29sdmVkID8gJ2dyZXkuNTAnIDogJ2JhY2tncm91bmQucGFwZXInLFxuICAgICAgICAgICAgICAgIG9wYWNpdHk6IGFsZXJ0LmlzUmVzb2x2ZWQgPyAwLjcgOiAxLFxuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8TGlzdEl0ZW1JY29uPlxuICAgICAgICAgICAgICAgIDxCb3ggY29sb3I9e2Ake2dldFNldmVyaXR5Q29sb3IoYWxlcnQuc2V2ZXJpdHkpfS5tYWluYH0+XG4gICAgICAgICAgICAgICAgICB7Z2V0QWxlcnRJY29uKGFsZXJ0LnR5cGUpfVxuICAgICAgICAgICAgICAgIDwvQm94PlxuICAgICAgICAgICAgICA8L0xpc3RJdGVtSWNvbj5cbiAgICAgICAgICAgICAgPExpc3RJdGVtVGV4dFxuICAgICAgICAgICAgICAgIHByaW1hcnk9e1xuICAgICAgICAgICAgICAgICAgPEJveCBkaXNwbGF5PVwiZmxleFwiIGFsaWduSXRlbXM9XCJjZW50ZXJcIiBqdXN0aWZ5Q29udGVudD1cInNwYWNlLWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cInN1YnRpdGxlMlwiIGZvbnRXZWlnaHQ9XCJib2xkXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2FsZXJ0LnRpdGxlfVxuICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgICAgICAgICAgICAgIDxCb3ggZGlzcGxheT1cImZsZXhcIiBhbGlnbkl0ZW1zPVwiY2VudGVyXCIgZ2FwPXsxfT5cbiAgICAgICAgICAgICAgICAgICAgICA8Q2hpcFxuICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw9e2FsZXJ0LnNldmVyaXR5LnRvVXBwZXJDYXNlKCl9XG4gICAgICAgICAgICAgICAgICAgICAgICBjb2xvcj17Z2V0U2V2ZXJpdHlDb2xvcihhbGVydC5zZXZlcml0eSkgYXMgYW55fVxuICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtYWxsXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lZFwiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICB7YWxlcnQuaXNSZXNvbHZlZCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8Q2hlY2tDaXJjbGUgY29sb3I9XCJzdWNjZXNzXCIgZm9udFNpemU9XCJzbWFsbFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgPC9Cb3g+XG4gICAgICAgICAgICAgICAgICA8L0JveD5cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgc2Vjb25kYXJ5PXtcbiAgICAgICAgICAgICAgICAgIDxCb3ggbXQ9ezAuNX0+XG4gICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJib2R5MlwiIGNvbG9yPVwidGV4dC5zZWNvbmRhcnlcIiBtYj17MC41fT5cbiAgICAgICAgICAgICAgICAgICAgICB7YWxlcnQuZGVzY3JpcHRpb259XG4gICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImNhcHRpb25cIiBjb2xvcj1cInRleHQuc2Vjb25kYXJ5XCI+XG4gICAgICAgICAgICAgICAgICAgICAge2Zvcm1hdFRpbWVBZ28oYWxlcnQudGltZXN0YW1wKX1cbiAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxuICAgICAgICAgICAgICAgICAgPC9Cb3g+XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9MaXN0SXRlbT5cbiAgICAgICAgICApKX1cbiAgICAgICAgPC9MaXN0PlxuICAgICAgKX1cbiAgICA8L0JveD5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IEFsZXJ0c1BhbmVsO1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwiTGlzdCIsIkxpc3RJdGVtIiwiTGlzdEl0ZW1UZXh0IiwiTGlzdEl0ZW1JY29uIiwiVHlwb2dyYXBoeSIsIkJveCIsIkNoaXAiLCJBbGVydCIsIkFsZXJ0VGl0bGUiLCJXYXJuaW5nIiwiRXJyb3IiLCJJbmZvIiwiQ2hlY2tDaXJjbGUiLCJTZWN1cml0eSIsIlRyZW5kaW5nVXAiLCJBbGVydHNQYW5lbCIsImFsZXJ0cyIsIm1vY2tBbGVydHMiLCJpZCIsInR5cGUiLCJzZXZlcml0eSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJ0aW1lc3RhbXAiLCJEYXRlIiwibm93IiwidG9JU09TdHJpbmciLCJpc1Jlc29sdmVkIiwiZGlzcGxheUFsZXJ0cyIsImxlbmd0aCIsImdldEFsZXJ0SWNvbiIsImdldFNldmVyaXR5Q29sb3IiLCJnZXRBbGVydFNldmVyaXR5IiwiZm9ybWF0VGltZUFnbyIsImFsZXJ0VGltZSIsImRpZmZJbk1pbnV0ZXMiLCJNYXRoIiwiZmxvb3IiLCJnZXRUaW1lIiwiZGlmZkluSG91cnMiLCJkaWZmSW5EYXlzIiwic3giLCJtYXhIZWlnaHQiLCJvdmVyZmxvdyIsImRlbnNlIiwic2xpY2UiLCJtYXAiLCJhbGVydCIsIm1iIiwiYm9yZGVyIiwiYm9yZGVyQ29sb3IiLCJib3JkZXJSYWRpdXMiLCJiZ2NvbG9yIiwib3BhY2l0eSIsImNvbG9yIiwicHJpbWFyeSIsImRpc3BsYXkiLCJhbGlnbkl0ZW1zIiwianVzdGlmeUNvbnRlbnQiLCJ2YXJpYW50IiwiZm9udFdlaWdodCIsImdhcCIsImxhYmVsIiwidG9VcHBlckNhc2UiLCJzaXplIiwiZm9udFNpemUiLCJzZWNvbmRhcnkiLCJtdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/AlertsPanel.tsx\n");

/***/ })

};
;