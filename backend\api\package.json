{"name": "solana-trading-api", "version": "1.0.0", "description": "API server for Solana Real-Time Trading Intelligence System", "main": "dist/server.js", "scripts": {"dev": "node src/simple-server.js", "dev:ts": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "type-check": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "docker:build": "docker build -t solana-trading-api .", "docker:run": "docker run -p 3004:3004 solana-trading-api"}, "dependencies": {"@solana/web3.js": "^1.87.6", "@types/bcryptjs": "^2.4.4", "@types/compression": "^1.7.4", "@types/cors": "^2.8.15", "@types/express": "^4.17.20", "@types/helmet": "^4.0.0", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.7", "@types/node": "^20.8.7", "axios": "^1.6.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "http-proxy-middleware": "^2.0.6", "ioredis": "^5.3.2", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "pg": "^8.11.3", "pg-pool": "^3.6.1", "winston": "^3.11.0"}, "devDependencies": {"@types/jest": "^29.5.7", "@types/pg": "^8.10.7", "@types/supertest": "^2.0.15", "@typescript-eslint/eslint-plugin": "^6.9.1", "@typescript-eslint/parser": "^6.9.1", "eslint": "^8.52.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "rimraf": "^5.0.5", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["solana", "trading", "api", "blockchain", "defi", "analytics"], "author": "HectorTa1989", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/HectorTa1989/solana-trading-intelligence.git"}, "bugs": {"url": "https://github.com/HectorTa1989/solana-trading-intelligence/issues"}, "homepage": "https://github.com/HectorTa1989/solana-trading-intelligence#readme"}