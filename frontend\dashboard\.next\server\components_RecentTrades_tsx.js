"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "components_RecentTrades_tsx";
exports.ids = ["components_RecentTrades_tsx"];
exports.modules = {

/***/ "__barrel_optimize__?names=Box,Chip,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!../../node_modules/@mui/material/index.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Box,Chip,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!../../node_modules/@mui/material/index.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Box: () => (/* reexport default from dynamic */ _Box__WEBPACK_IMPORTED_MODULE_0___default.a),\n/* harmony export */   Chip: () => (/* reexport default from dynamic */ _Chip__WEBPACK_IMPORTED_MODULE_1___default.a),\n/* harmony export */   Paper: () => (/* reexport default from dynamic */ _Paper__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   Table: () => (/* reexport default from dynamic */ _Table__WEBPACK_IMPORTED_MODULE_3___default.a),\n/* harmony export */   TableBody: () => (/* reexport default from dynamic */ _TableBody__WEBPACK_IMPORTED_MODULE_4___default.a),\n/* harmony export */   TableCell: () => (/* reexport default from dynamic */ _TableCell__WEBPACK_IMPORTED_MODULE_5___default.a),\n/* harmony export */   TableContainer: () => (/* reexport default from dynamic */ _TableContainer__WEBPACK_IMPORTED_MODULE_6___default.a),\n/* harmony export */   TableHead: () => (/* reexport default from dynamic */ _TableHead__WEBPACK_IMPORTED_MODULE_7___default.a),\n/* harmony export */   TableRow: () => (/* reexport default from dynamic */ _TableRow__WEBPACK_IMPORTED_MODULE_8___default.a),\n/* harmony export */   Typography: () => (/* reexport default from dynamic */ _Typography__WEBPACK_IMPORTED_MODULE_9___default.a)\n/* harmony export */ });\n/* harmony import */ var _Box__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Box */ \"../../node_modules/@mui/material/node/Box/index.js\");\n/* harmony import */ var _Box__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Box__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Chip__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Chip */ \"../../node_modules/@mui/material/node/Chip/index.js\");\n/* harmony import */ var _Chip__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_Chip__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Paper__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Paper */ \"../../node_modules/@mui/material/node/Paper/index.js\");\n/* harmony import */ var _Paper__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_Paper__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Table__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Table */ \"../../node_modules/@mui/material/node/Table/index.js\");\n/* harmony import */ var _Table__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_Table__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _TableBody__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./TableBody */ \"../../node_modules/@mui/material/node/TableBody/index.js\");\n/* harmony import */ var _TableBody__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_TableBody__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _TableCell__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./TableCell */ \"../../node_modules/@mui/material/node/TableCell/index.js\");\n/* harmony import */ var _TableCell__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_TableCell__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _TableContainer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./TableContainer */ \"../../node_modules/@mui/material/node/TableContainer/index.js\");\n/* harmony import */ var _TableContainer__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_TableContainer__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _TableHead__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./TableHead */ \"../../node_modules/@mui/material/node/TableHead/index.js\");\n/* harmony import */ var _TableHead__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_TableHead__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _TableRow__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./TableRow */ \"../../node_modules/@mui/material/node/TableRow/index.js\");\n/* harmony import */ var _TableRow__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_TableRow__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _Typography__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./Typography */ \"../../node_modules/@mui/material/node/Typography/index.js\");\n/* harmony import */ var _Typography__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_Typography__WEBPACK_IMPORTED_MODULE_9__);\n/**\n * @mui/material v5.18.0\n *\n * @license MIT\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */ /* eslint-disable import/export */ \n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1Cb3gsQ2hpcCxQYXBlcixUYWJsZSxUYWJsZUJvZHksVGFibGVDZWxsLFRhYmxlQ29udGFpbmVyLFRhYmxlSGVhZCxUYWJsZVJvdyxUeXBvZ3JhcGh5IT0hLi4vLi4vbm9kZV9tb2R1bGVzL0BtdWkvbWF0ZXJpYWwvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDc0M7QUFDRTtBQUNFO0FBQ0E7QUFDUTtBQUNBO0FBQ1U7QUFDVjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc29sYW5hLXRyYWRpbmctZGFzaGJvYXJkLy4uLy4uL25vZGVfbW9kdWxlcy9AbXVpL21hdGVyaWFsL2luZGV4LmpzP2IxMjEiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbXVpL21hdGVyaWFsIHY1LjE4LjBcbiAqXG4gKiBAbGljZW5zZSBNSVRcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIE1JVCBsaWNlbnNlIGZvdW5kIGluIHRoZVxuICogTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICovIC8qIGVzbGludC1kaXNhYmxlIGltcG9ydC9leHBvcnQgKi8gXG5leHBvcnQgeyBkZWZhdWx0IGFzIEJveCB9IGZyb20gXCIuL0JveFwiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENoaXAgfSBmcm9tIFwiLi9DaGlwXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgUGFwZXIgfSBmcm9tIFwiLi9QYXBlclwiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFRhYmxlIH0gZnJvbSBcIi4vVGFibGVcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBUYWJsZUJvZHkgfSBmcm9tIFwiLi9UYWJsZUJvZHlcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBUYWJsZUNlbGwgfSBmcm9tIFwiLi9UYWJsZUNlbGxcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBUYWJsZUNvbnRhaW5lciB9IGZyb20gXCIuL1RhYmxlQ29udGFpbmVyXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgVGFibGVIZWFkIH0gZnJvbSBcIi4vVGFibGVIZWFkXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgVGFibGVSb3cgfSBmcm9tIFwiLi9UYWJsZVJvd1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFR5cG9ncmFwaHkgfSBmcm9tIFwiLi9UeXBvZ3JhcGh5XCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Box,Chip,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!../../node_modules/@mui/material/index.js\n");

/***/ }),

/***/ "./components/RecentTrades.tsx":
/*!*************************************!*\
  !*** ./components/RecentTrades.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_Chip_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Chip,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"__barrel_optimize__?names=Box,Chip,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!../../node_modules/@mui/material/index.js\");\n/* harmony import */ var _mui_icons_material_TrendingUp__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mui/icons-material/TrendingUp */ \"@mui/icons-material/TrendingUp\");\n/* harmony import */ var _mui_icons_material_TrendingUp__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_mui_icons_material_TrendingUp__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _mui_icons_material_TrendingDown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/icons-material/TrendingDown */ \"@mui/icons-material/TrendingDown\");\n/* harmony import */ var _mui_icons_material_TrendingDown__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_mui_icons_material_TrendingDown__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _mui_icons_material_SwapHoriz__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/icons-material/SwapHoriz */ \"@mui/icons-material/SwapHoriz\");\n/* harmony import */ var _mui_icons_material_SwapHoriz__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_mui_icons_material_SwapHoriz__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _mui_icons_material_Warning__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mui/icons-material/Warning */ \"@mui/icons-material/Warning\");\n/* harmony import */ var _mui_icons_material_Warning__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_mui_icons_material_Warning__WEBPACK_IMPORTED_MODULE_5__);\n\n\n\n\n\n\n\nconst RecentTrades = ({ trades })=>{\n    // Mock data for demonstration\n    const mockTrades = [\n        {\n            signature: \"5VfYmGC9L2VTAhBjEhd4KGX9h8b4c3d2e1f0g9h8i7j6k5l4m3n2o1p0q9r8s7t6u5v4w3x2y1z0\",\n            walletAddress: \"7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU\",\n            dex: \"Raydium\",\n            tradeType: \"buy\",\n            tokenInSymbol: \"SOL\",\n            tokenOutSymbol: \"BONK\",\n            amountIn: 1.5,\n            amountOut: 1500000,\n            price: 0.000001,\n            blockTime: new Date(Date.now() - 2 * 60 * 1000).toISOString(),\n            isSuspicious: false\n        },\n        {\n            signature: \"4UeXhGC8L1VTAhBjEhd3KGX8h7b3c2d1e0f9g8h7i6j5k4l3m2n1o0p9q8r7s6t5u4v3w2x1y0z9\",\n            walletAddress: \"9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM\",\n            dex: \"Jupiter\",\n            tradeType: \"sell\",\n            tokenInSymbol: \"WIF\",\n            tokenOutSymbol: \"USDC\",\n            amountIn: 1000,\n            amountOut: 2500,\n            price: 2.5,\n            blockTime: new Date(Date.now() - 5 * 60 * 1000).toISOString(),\n            isSuspicious: true\n        },\n        {\n            signature: \"3TdWfGC7L0VTAhBjEhd2KGX7h6b2c1d0e9f8g7h6i5j4k3l2m1n0o9p8q7r6s5t4u3v2w1x0y9z8\",\n            walletAddress: \"BQcdHdAQW1hczDbBi9hiegXAR7A98Q9jx3X3iBBBDiq4\",\n            dex: \"Orca\",\n            tradeType: \"swap\",\n            tokenInSymbol: \"USDC\",\n            tokenOutSymbol: \"SOL\",\n            amountIn: 150,\n            amountOut: 1,\n            price: 150,\n            blockTime: new Date(Date.now() - 8 * 60 * 1000).toISOString(),\n            isSuspicious: false\n        },\n        {\n            signature: \"2ScVeGC6L9VTAhBjEhd1KGX6h5b1c0d9e8f7g6h5i4j3k2l1m0n9o8p7q6r5s4t3u2v1w0x9y8z7\",\n            walletAddress: \"DhJ4hdhJSkQyQGC9MjjLykbs4RGGDrvkayJA3S8PiQdG\",\n            dex: \"Pump.fun\",\n            tradeType: \"buy\",\n            tokenInSymbol: \"SOL\",\n            tokenOutSymbol: \"PEPE\",\n            amountIn: 0.5,\n            amountOut: 50000,\n            price: 0.00001,\n            blockTime: new Date(Date.now() - 12 * 60 * 1000).toISOString(),\n            isSuspicious: false\n        }\n    ];\n    const displayTrades = trades.length > 0 ? trades : mockTrades;\n    const getTradeIcon = (tradeType)=>{\n        switch(tradeType){\n            case \"buy\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_icons_material_TrendingUp__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    color: \"success\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\RecentTrades.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 16\n                }, undefined);\n            case \"sell\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_icons_material_TrendingDown__WEBPACK_IMPORTED_MODULE_3___default()), {\n                    color: \"error\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\RecentTrades.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 16\n                }, undefined);\n            case \"swap\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_icons_material_SwapHoriz__WEBPACK_IMPORTED_MODULE_4___default()), {\n                    color: \"info\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\RecentTrades.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_icons_material_SwapHoriz__WEBPACK_IMPORTED_MODULE_4___default()), {}, void 0, false, {\n                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\RecentTrades.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const getTradeTypeColor = (tradeType)=>{\n        switch(tradeType){\n            case \"buy\":\n                return \"success\";\n            case \"sell\":\n                return \"error\";\n            case \"swap\":\n                return \"info\";\n            default:\n                return \"default\";\n        }\n    };\n    const formatAddress = (address)=>{\n        return `${address.slice(0, 4)}...${address.slice(-4)}`;\n    };\n    const formatTimeAgo = (timestamp)=>{\n        const now = new Date();\n        const tradeTime = new Date(timestamp);\n        const diffInMinutes = Math.floor((now.getTime() - tradeTime.getTime()) / (1000 * 60));\n        if (diffInMinutes < 1) return \"Just now\";\n        if (diffInMinutes < 60) return `${diffInMinutes}m ago`;\n        const diffInHours = Math.floor(diffInMinutes / 60);\n        return `${diffInHours}h ago`;\n    };\n    const formatNumber = (num)=>{\n        if (num >= 1000000) {\n            return `${(num / 1000000).toFixed(2)}M`;\n        }\n        if (num >= 1000) {\n            return `${(num / 1000).toFixed(2)}K`;\n        }\n        return num.toFixed(6);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__.TableContainer, {\n        component: _barrel_optimize_names_Box_Chip_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__.Paper,\n        sx: {\n            maxHeight: 400\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__.Table, {\n            stickyHeader: true,\n            size: \"small\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__.TableRow, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                children: \"Type\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\RecentTrades.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                children: \"Wallet\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\RecentTrades.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                children: \"DEX\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\RecentTrades.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                children: \"Trade\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\RecentTrades.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                align: \"right\",\n                                children: \"Amount\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\RecentTrades.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                align: \"right\",\n                                children: \"Price\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\RecentTrades.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                children: \"Time\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\RecentTrades.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                children: \"Status\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\RecentTrades.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\RecentTrades.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\RecentTrades.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__.TableBody, {\n                    children: displayTrades.map((trade)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__.TableRow, {\n                            sx: {\n                                \"&:last-child td, &:last-child th\": {\n                                    border: 0\n                                },\n                                backgroundColor: trade.isSuspicious ? \"error.light\" : \"inherit\",\n                                opacity: trade.isSuspicious ? 0.8 : 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        gap: 1,\n                                        children: [\n                                            getTradeIcon(trade.tradeType),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__.Chip, {\n                                                label: trade.tradeType.toUpperCase(),\n                                                color: getTradeTypeColor(trade.tradeType),\n                                                size: \"small\",\n                                                variant: \"outlined\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\RecentTrades.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\RecentTrades.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\RecentTrades.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__.Typography, {\n                                        variant: \"body2\",\n                                        fontFamily: \"monospace\",\n                                        children: formatAddress(trade.walletAddress)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\RecentTrades.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\RecentTrades.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__.Chip, {\n                                        label: trade.dex,\n                                        size: \"small\",\n                                        variant: \"outlined\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\RecentTrades.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\RecentTrades.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__.Typography, {\n                                        variant: \"body2\",\n                                        children: [\n                                            trade.tokenInSymbol,\n                                            \" → \",\n                                            trade.tokenOutSymbol\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\RecentTrades.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\RecentTrades.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                    align: \"right\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__.Typography, {\n                                                variant: \"body2\",\n                                                children: [\n                                                    formatNumber(trade.amountIn),\n                                                    \" \",\n                                                    trade.tokenInSymbol\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\RecentTrades.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__.Typography, {\n                                                variant: \"caption\",\n                                                color: \"text.secondary\",\n                                                children: [\n                                                    formatNumber(trade.amountOut),\n                                                    \" \",\n                                                    trade.tokenOutSymbol\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\RecentTrades.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\RecentTrades.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\RecentTrades.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                    align: \"right\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__.Typography, {\n                                        variant: \"body2\",\n                                        fontFamily: \"monospace\",\n                                        children: [\n                                            \"$\",\n                                            trade.price.toFixed(6)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\RecentTrades.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\RecentTrades.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__.Typography, {\n                                        variant: \"caption\",\n                                        color: \"text.secondary\",\n                                        children: formatTimeAgo(trade.blockTime)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\RecentTrades.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\RecentTrades.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                    children: trade.isSuspicious ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__.Chip, {\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_icons_material_Warning__WEBPACK_IMPORTED_MODULE_5___default()), {}, void 0, false, void 0, void 0),\n                                        label: \"Suspicious\",\n                                        color: \"warning\",\n                                        size: \"small\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\RecentTrades.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 19\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__.Chip, {\n                                        label: \"Normal\",\n                                        color: \"success\",\n                                        size: \"small\",\n                                        variant: \"outlined\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\RecentTrades.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\RecentTrades.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, trade.signature, true, {\n                            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\RecentTrades.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\RecentTrades.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\RecentTrades.tsx\",\n            lineNumber: 153,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Project\\\\Crypto\\\\Solana Real-Time Trading Intelligence System\\\\frontend\\\\dashboard\\\\components\\\\RecentTrades.tsx\",\n        lineNumber: 152,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RecentTrades);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/RecentTrades.tsx\n");

/***/ })

};
;